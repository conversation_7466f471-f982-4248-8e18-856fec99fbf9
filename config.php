<?php
/**
 * Emby Server Registration System Configuration
 * 
 * Configure your Emby server settings and other system parameters here
 */

// Emby Server Configuration
define('EMBY_SERVER_URL', 'http://embyjames.ddns.net:8096'); // Change to your Emby server URL
define('EMBY_API_KEY', '91a65d106bce4754b8a2edea45adaed7'); // Replace with your actual API key

// Registration Settings
define('ALLOW_REGISTRATION', true); // Set to false to disable registration
define('DEFAULT_USER_POLICY', [
    'IsAdministrator' => false,
    'IsHidden' => false,
    'IsDisabled' => false,
    'EnableUserPreferenceAccess' => true,
    'EnableRemoteControlOfOtherUsers' => false,
    'EnableSharedDeviceControl' => false,
    'EnableRemoteAccess' => true,
    'EnableLiveTvManagement' => false,
    'EnableLiveTvAccess' => true,
    'EnableMediaPlayback' => true,
    'EnableAudioPlaybackTranscoding' => true,
    'EnableVideoPlaybackTranscoding' => true,
    'EnablePlaybackRemuxing' => true,
    'EnableContentDeletion' => false,
    'EnableContentDownloading' => false,
    'EnableSyncTranscoding' => false,
    'EnableMediaConversion' => false,
    'EnableAllDevices' => true,
    'EnableAllChannels' => true,
    'EnableAllFolders' => true,
    'InvalidLoginAttemptCount' => 0,
    'EnablePublicSharing' => false
]);

// Email Configuration (Optional)
define('ENABLE_EMAIL_NOTIFICATIONS', false);
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Emby Registration System');
define('ADMIN_EMAIL', '<EMAIL>');

// Database Configuration (Optional - for logging)
define('ENABLE_DATABASE_LOGGING', false);
define('DB_HOST', 'localhost');
define('DB_NAME', 'emby_registration');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'Wxmujwsofu@1234');

// Security Settings
define('ENABLE_CAPTCHA', false); // Set to true to enable reCAPTCHA
define('RECAPTCHA_SITE_KEY', 'your-site-key');
define('RECAPTCHA_SECRET_KEY', 'your-secret-key');

// Validation Rules
define('MIN_USERNAME_LENGTH', 3);
define('MAX_USERNAME_LENGTH', 20);
define('MIN_PASSWORD_LENGTH', 6);
define('MAX_PASSWORD_LENGTH', 50);

// Rate Limiting
define('ENABLE_RATE_LIMITING', true);
define('MAX_REGISTRATIONS_PER_IP', 5); // Per hour
define('MAX_REGISTRATIONS_PER_DAY', 10); // Per day

// Error Messages
define('ERROR_MESSAGES', [
    'REGISTRATION_DISABLED' => 'User registration is currently disabled.',
    'INVALID_INPUT' => 'Please fill in all required fields correctly.',
    'USERNAME_TOO_SHORT' => 'Username must be at least ' . MIN_USERNAME_LENGTH . ' characters long.',
    'USERNAME_TOO_LONG' => 'Username cannot exceed ' . MAX_USERNAME_LENGTH . ' characters.',
    'PASSWORD_TOO_SHORT' => 'Password must be at least ' . MIN_PASSWORD_LENGTH . ' characters long.',
    'PASSWORD_TOO_LONG' => 'Password cannot exceed ' . MAX_PASSWORD_LENGTH . ' characters.',
    'INVALID_EMAIL' => 'Please enter a valid email address.',
    'USERNAME_EXISTS' => 'Username already exists. Please choose a different username.',
    'EMBY_CONNECTION_ERROR' => 'Unable to connect to Emby server. Please try again later.',
    'EMBY_API_ERROR' => 'Error creating user account. Please contact administrator.',
    'RATE_LIMIT_EXCEEDED' => 'Too many registration attempts. Please try again later.',
    'CAPTCHA_FAILED' => 'Please complete the captcha verification.',
    'GENERAL_ERROR' => 'An unexpected error occurred. Please try again.'
]);

// Success Messages
define('SUCCESS_MESSAGES', [
    'REGISTRATION_SUCCESS' => 'Account created successfully! You can now log in to Emby.',
    'EMAIL_SENT' => 'A confirmation email has been sent to your email address.'
]);

// Timezone
date_default_timezone_set('UTC');

// Error Reporting (Set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
