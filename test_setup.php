<?php
/**
 * Test Setup for Emby Registration System
 * Run this file to verify your configuration
 */

require_once 'config.php';
require_once 'functions.php';

echo "<h1>Emby Registration System - Setup Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

// Test 1: PHP Version
echo "<div class='test-section'>";
echo "<h2>1. PHP Version Check</h2>";
$phpVersion = phpversion();
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "<span class='success'>✅ PHP Version: $phpVersion (OK)</span>";
} else {
    echo "<span class='error'>❌ PHP Version: $phpVersion (Requires 7.4+)</span>";
}
echo "</div>";

// Test 2: Required Extensions
echo "<div class='test-section'>";
echo "<h2>2. Required Extensions</h2>";
$extensions = ['curl', 'json', 'session'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span class='success'>✅ $ext extension: Loaded</span><br>";
    } else {
        echo "<span class='error'>❌ $ext extension: Not loaded</span><br>";
    }
}
echo "</div>";

// Test 3: Configuration
echo "<div class='test-section'>";
echo "<h2>3. Configuration Check</h2>";
echo "<strong>Emby Server URL:</strong> " . EMBY_SERVER_URL . "<br>";
echo "<strong>API Key:</strong> " . (EMBY_API_KEY !== 'YOUR_API_KEY_HERE' ? 'Configured' : '<span class="error">Not configured</span>') . "<br>";
echo "<strong>Registration Enabled:</strong> " . (ALLOW_REGISTRATION ? '<span class="success">Yes</span>' : '<span class="warning">No</span>') . "<br>";
echo "<strong>Database Logging:</strong> " . (ENABLE_DATABASE_LOGGING ? '<span class="info">Enabled</span>' : 'Disabled') . "<br>";
echo "<strong>Email Notifications:</strong> " . (ENABLE_EMAIL_NOTIFICATIONS ? '<span class="info">Enabled</span>' : 'Disabled') . "<br>";
echo "<strong>Rate Limiting:</strong> " . (ENABLE_RATE_LIMITING ? '<span class="info">Enabled</span>' : 'Disabled') . "<br>";
echo "</div>";

// Test 4: Emby Server Connection
echo "<div class='test-section'>";
echo "<h2>4. Emby Server Connection</h2>";
if (EMBY_API_KEY === 'YOUR_API_KEY_HERE') {
    echo "<span class='warning'>⚠️ Cannot test connection: API key not configured</span>";
} else {
    $connectionTest = testEmbyConnection();
    if ($connectionTest) {
        echo "<span class='success'>✅ Emby Server: Connected successfully</span>";
        
        // Get server info
        $serverInfo = makeEmbyApiRequest('System/Info');
        if ($serverInfo['success'] && isset($serverInfo['data']['ServerName'])) {
            echo "<br><strong>Server Name:</strong> " . htmlspecialchars($serverInfo['data']['ServerName']);
            echo "<br><strong>Version:</strong> " . htmlspecialchars($serverInfo['data']['Version'] ?? 'Unknown');
        }
    } else {
        echo "<span class='error'>❌ Emby Server: Connection failed</span>";
        echo "<br><small>Check your EMBY_SERVER_URL and EMBY_API_KEY in config.php</small>";
    }
}
echo "</div>";

// Test 5: Database Connection (if enabled)
if (ENABLE_DATABASE_LOGGING) {
    echo "<div class='test-section'>";
    echo "<h2>5. Database Connection</h2>";
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        echo "<span class='success'>✅ Database: Connected successfully</span>";
        echo "<br><strong>Host:</strong> " . DB_HOST;
        echo "<br><strong>Database:</strong> " . DB_NAME;
        
        // Check if tables exist
        $tables = ['users', 'registration_log', 'rate_limiting'];
        foreach ($tables as $table) {
            $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                echo "<br><span class='success'>✅ Table '$table': Exists</span>";

                // Show table info for users table
                if ($table === 'users') {
                    try {
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM users");
                        $userCount = $countStmt->fetchColumn();
                        echo " ($userCount users)";
                    } catch (Exception $e) {
                        echo " (error counting users)";
                    }
                }
            } else {
                echo "<br><span class='warning'>⚠️ Table '$table': Not found (run setup_database.php)</span>";
            }
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Database: Connection failed</span>";
        echo "<br><small>Error: " . htmlspecialchars($e->getMessage()) . "</small>";
    }
    echo "</div>";
}

// Test 6: File Permissions
echo "<div class='test-section'>";
echo "<h2>6. File Permissions</h2>";
$files = ['config.php', 'functions.php', 'register.php', 'index.html'];
foreach ($files as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<span class='success'>✅ $file: Readable</span><br>";
        } else {
            echo "<span class='error'>❌ $file: Not readable</span><br>";
        }
    } else {
        echo "<span class='error'>❌ $file: File not found</span><br>";
    }
}
echo "</div>";

// Test 7: Session Support
echo "<div class='test-section'>";
echo "<h2>7. Session Support</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<span class='success'>✅ Sessions: Working</span>";
} else {
    echo "<span class='error'>❌ Sessions: Not working</span>";
}
echo "</div>";

// Summary
echo "<div class='test-section'>";
echo "<h2>Setup Summary</h2>";
$issues = [];

if (version_compare(phpversion(), '7.4.0', '<')) {
    $issues[] = "PHP version too old";
}

if (!extension_loaded('curl')) {
    $issues[] = "cURL extension missing";
}

if (EMBY_API_KEY === 'YOUR_API_KEY_HERE') {
    $issues[] = "API key not configured";
}

if (!ALLOW_REGISTRATION) {
    $issues[] = "Registration is disabled";
}

if (empty($issues)) {
    echo "<span class='success'>🎉 Setup looks good! You can now test the registration system.</span>";
    echo "<br><br><a href='index.html' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Registration Form</a>";
} else {
    echo "<span class='error'>⚠️ Issues found:</span><br>";
    foreach ($issues as $issue) {
        echo "• $issue<br>";
    }
    echo "<br>Please fix these issues before using the registration system.";
}
echo "</div>";

echo "<hr>";
echo "<small>Test completed at " . date('Y-m-d H:i:s') . "</small>";
?>
