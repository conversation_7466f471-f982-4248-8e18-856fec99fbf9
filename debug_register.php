<?php
/**
 * Debug Registration Process
 */

require_once 'config.php';
require_once 'functions.php';

echo "<h1>Registration Debug</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>POST Data Received</h2>";
    
    // Get raw input
    $rawInput = file_get_contents('php://input');
    echo "<strong>Raw Input:</strong><br><pre>" . htmlspecialchars($rawInput) . "</pre><br>";
    
    // Try to decode JSON
    $jsonData = json_decode($rawInput, true);
    echo "<strong>JSON Decoded:</strong><br><pre>" . print_r($jsonData, true) . "</pre><br>";
    
    // Check $_POST
    echo "<strong>\$_POST Data:</strong><br><pre>" . print_r($_POST, true) . "</pre><br>";
    
    // Test input processing
    $input = $jsonData ?: $_POST;
    echo "<strong>Final Input:</strong><br><pre>" . print_r($input, true) . "</pre><br>";
    
    // Test sanitization
    if (!empty($input)) {
        $sanitized = sanitizeInput($input);
        echo "<strong>Sanitized Data:</strong><br><pre>" . print_r($sanitized, true) . "</pre><br>";
        
        // Test validation
        $errors = validateInput($sanitized);
        if (empty($errors)) {
            echo "<span class='success'>✅ Validation passed</span><br>";
        } else {
            echo "<span class='error'>❌ Validation errors:</span><br>";
            foreach ($errors as $error) {
                echo "- " . htmlspecialchars($error) . "<br>";
            }
        }
        
        // Test CSRF token
        $csrfToken = $sanitized['csrf_token'] ?? '';
        if (verifyCSRFToken($csrfToken)) {
            echo "<span class='success'>✅ CSRF token valid</span><br>";
        } else {
            echo "<span class='error'>❌ CSRF token invalid</span><br>";
            echo "Received token: " . htmlspecialchars($csrfToken) . "<br>";
            echo "Session token: " . htmlspecialchars($_SESSION['csrf_token'] ?? 'not set') . "<br>";
        }
        
        // Test Emby connection
        if (testEmbyConnection()) {
            echo "<span class='success'>✅ Emby server connection OK</span><br>";
        } else {
            echo "<span class='error'>❌ Emby server connection failed</span><br>";
        }
    }
    
} else {
    echo "<h2>Test Form</h2>";
    echo "<p>Use this form to test the registration process:</p>";
    
    // Generate CSRF token
    $csrfToken = generateCSRFToken();
    
    echo '<form method="POST" action="debug_register.php">
        <input type="hidden" name="csrf_token" value="' . htmlspecialchars($csrfToken) . '">
        <p>Username: <input type="text" name="username" value="testuser" required></p>
        <p>Password: <input type="password" name="password" value="testpass123" required></p>
        <p>Confirm Password: <input type="password" name="confirm_password" value="testpass123" required></p>
        <p>Email: <input type="email" name="email" value="<EMAIL>"></p>
        <p>Full Name: <input type="text" name="fullname" value="Test User"></p>
        <p><input type="submit" value="Test Registration"></p>
    </form>';
    
    echo "<h2>Current Configuration</h2>";
    echo "Emby Server URL: " . EMBY_SERVER_URL . "<br>";
    echo "API Key: " . substr(EMBY_API_KEY, 0, 8) . "...<br>";
    echo "Registration Enabled: " . (ALLOW_REGISTRATION ? 'Yes' : 'No') . "<br>";
    echo "CSRF Token: " . htmlspecialchars($csrfToken) . "<br>";
}
?>
