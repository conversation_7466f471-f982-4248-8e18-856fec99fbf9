<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Emby Server</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-5 col-lg-4">
                <div class="card shadow-lg">
                    <div class="card-header text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <svg width="40" height="40" viewBox="0 0 100 100" class="me-3">
                                <circle cx="50" cy="50" r="45" fill="#00a4dc"/>
                                <text x="50" y="60" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">E</text>
                            </svg>
                            <h3 class="mb-0">Reset Password</h3>
                        </div>
                        <p class="mb-0 text-white-50">Enter your email to reset your password</p>
                    </div>
                    <div class="card-body">
                        <!-- Alert Messages -->
                        <div id="alertContainer"></div>
                        
                        <!-- Reset Password Form -->
                        <form id="resetPasswordForm" novalidate>
                            <!-- CSRF Token -->
                            <input type="hidden" id="csrfToken" name="csrf_token" value="">
                            
                            <!-- Email -->
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email Address <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" required
                                       placeholder="Enter your registered email address">
                                <div class="invalid-feedback">
                                    Please enter a valid email address.
                                </div>
                                <div class="form-text">
                                    We'll send you a link to reset your password.
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                                </button>
                            </div>
                        </form>
                        
                        <!-- Back to Login -->
                        <div class="text-center mt-3">
                            <p class="mb-0">Remember your password?</p>
                            <a href="http://embyjames.ddns.net:8096/" class="btn btn-link">
                                <i class="fas fa-arrow-left me-1"></i>Back to Emby Login
                            </a>
                        </div>
                        
                        <!-- Register Link -->
                        <div class="text-center mt-2">
                            <p class="mb-0">Don't have an account?</p>
                            <a href="index.html" class="btn btn-link">
                                <i class="fas fa-user-plus me-1"></i>Create Account
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Server Status -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <small class="text-muted">
                            <i class="fas fa-server me-1"></i>Server Status: 
                            <span id="serverStatus" class="badge bg-secondary">Checking...</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="successModalLabel">
                        <i class="fas fa-check-circle me-2"></i>Email Sent!
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <i class="fas fa-envelope text-success" style="font-size: 3rem;"></i>
                    <h4 class="mt-3">Check Your Email</h4>
                    <p class="mb-0">We've sent a password reset link to your email address.</p>
                    <p>Please check your inbox and follow the instructions.</p>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <small>The reset link will expire in 1 hour for security reasons.</small>
                    </div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-success" onclick="window.location.href='http://embyjames.ddns.net:8096/'">
                        <i class="fas fa-external-link-alt me-2"></i>Go to Emby Login
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get CSRF token
            fetchCSRFToken();
            
            // Check server status
            checkServerStatus();
            
            // Set up form submission
            document.getElementById('resetPasswordForm').addEventListener('submit', handleFormSubmit);
        });
        
        function fetchCSRFToken() {
            fetch('get_csrf_token.php')
                .then(response => response.json())
                .then(data => {
                    if (data.token) {
                        document.getElementById('csrfToken').value = data.token;
                    }
                })
                .catch(error => {
                    console.error('Error fetching CSRF token:', error);
                });
        }
        
        function checkServerStatus() {
            const statusElement = document.getElementById('serverStatus');
            
            fetch('check_server.php')
                .then(response => response.json())
                .then(data => {
                    if (data.online) {
                        statusElement.textContent = 'Online';
                        statusElement.className = 'badge bg-success';
                    } else {
                        statusElement.textContent = 'Offline';
                        statusElement.className = 'badge bg-danger';
                    }
                })
                .catch(error => {
                    statusElement.textContent = 'Unknown';
                    statusElement.className = 'badge bg-warning';
                });
        }
        
        function handleFormSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return;
            }
            
            setLoadingState(submitBtn, true);
            
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            fetch('reset_password.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                setLoadingState(submitBtn, false);
                
                if (data.success) {
                    showSuccessModal();
                    form.reset();
                    form.classList.remove('was-validated');
                } else {
                    showAlert('danger', data.error || 'Failed to send reset email. Please try again.');
                }
            })
            .catch(error => {
                setLoadingState(submitBtn, false);
                console.error('Reset password error:', error);
                showAlert('danger', 'An unexpected error occurred. Please try again.');
            });
        }
        
        function setLoadingState(button, loading) {
            if (loading) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
            } else {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Reset Link';
            }
        }
        
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();
            
            const alertHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            
            alertContainer.innerHTML = alertHTML;
            
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 10000);
            
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        function showSuccessModal() {
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
        }
    </script>
</body>
</html>
