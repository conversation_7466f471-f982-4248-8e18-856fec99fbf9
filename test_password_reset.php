<?php
/**
 * Test Password Reset System
 */

require_once 'config.php';
require_once 'functions.php';

echo "<h1>Password Reset System Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// Check if database logging is enabled
if (!ENABLE_DATABASE_LOGGING) {
    echo "<span class='error'>❌ Database logging is not enabled. Please enable it in config.php</span><br>";
    exit;
}

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
        DB_USERNAME,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<span class='success'>✅ Database connection successful</span><br>";
    
    // Test 1: Check if required tables exist
    echo "<h2>1. Table Structure Check</h2>";
    $tables = ['users', 'password_reset_tokens', 'password_reset_log'];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "<span class='success'>✅ Table '$table': Exists</span><br>";
        } else {
            echo "<span class='error'>❌ Table '$table': Not found (run setup_database.php)</span><br>";
        }
    }
    
    // Test 2: Check if we have test users
    echo "<h2>2. Test Users Check</h2>";
    $userStmt = $pdo->query("SELECT COUNT(*) FROM users WHERE email IS NOT NULL AND email != ''");
    $usersWithEmail = $userStmt->fetchColumn();
    
    if ($usersWithEmail > 0) {
        echo "<span class='success'>✅ Found $usersWithEmail users with email addresses</span><br>";
        
        // Show sample users
        $sampleStmt = $pdo->query("SELECT username, email FROM users WHERE email IS NOT NULL AND email != '' LIMIT 3");
        $sampleUsers = $sampleStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<strong>Sample users for testing:</strong><br>";
        foreach ($sampleUsers as $user) {
            echo "- Username: " . htmlspecialchars($user['username']) . ", Email: " . htmlspecialchars($user['email']) . "<br>";
        }
    } else {
        echo "<span class='error'>❌ No users with email addresses found</span><br>";
        echo "<span class='info'>💡 Register some users with email addresses first</span><br>";
    }
    
    // Test 3: Test password reset token generation
    echo "<h2>3. Token Generation Test</h2>";
    $testToken = generatePasswordResetToken();
    if (strlen($testToken) === 64) {
        echo "<span class='success'>✅ Password reset token generation working</span><br>";
        echo "Sample token: " . htmlspecialchars(substr($testToken, 0, 16)) . "...<br>";
    } else {
        echo "<span class='error'>❌ Password reset token generation failed</span><br>";
    }
    
    // Test 4: Test email configuration
    echo "<h2>4. Email Configuration Test</h2>";
    if (ENABLE_EMAIL_NOTIFICATIONS) {
        echo "<span class='success'>✅ Email notifications enabled</span><br>";
        echo "SMTP Host: " . SMTP_HOST . "<br>";
        echo "SMTP Port: " . SMTP_PORT . "<br>";
        echo "From Email: " . FROM_EMAIL . "<br>";
    } else {
        echo "<span class='error'>❌ Email notifications disabled</span><br>";
        echo "<span class='info'>💡 Enable email notifications in config.php to test password reset emails</span><br>";
    }
    
    // Test 5: Test rate limiting
    echo "<h2>5. Rate Limiting Test</h2>";
    $testIP = '*************';
    
    // Clear any existing rate limit for test IP
    unset($_SESSION['password_reset_limit_' . md5($testIP)]);
    
    $attempts = 0;
    while (checkPasswordResetRateLimit($testIP) && $attempts < 5) {
        $attempts++;
    }
    
    if ($attempts === 3) {
        echo "<span class='success'>✅ Rate limiting working correctly (3 attempts allowed)</span><br>";
    } else {
        echo "<span class='error'>❌ Rate limiting not working correctly (allowed $attempts attempts)</span><br>";
    }
    
    // Test 6: Test database functions
    echo "<h2>6. Database Functions Test</h2>";
    
    // Test getUserByEmail
    if ($usersWithEmail > 0) {
        $testUser = $sampleUsers[0];
        $foundUser = getUserByEmail($testUser['email']);
        
        if ($foundUser && $foundUser['email'] === $testUser['email']) {
            echo "<span class='success'>✅ getUserByEmail() working correctly</span><br>";
        } else {
            echo "<span class='error'>❌ getUserByEmail() not working correctly</span><br>";
        }
    }
    
    // Test 7: Check password reset logs
    echo "<h2>7. Password Reset Logs</h2>";
    $logStmt = $pdo->query("SELECT COUNT(*) FROM password_reset_log");
    $logCount = $logStmt->fetchColumn();
    
    echo "Password reset attempts logged: $logCount<br>";
    
    if ($logCount > 0) {
        $recentStmt = $pdo->query("
            SELECT email, ip_address, success, created_at 
            FROM password_reset_log 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $recentLogs = $recentStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<strong>Recent password reset attempts:</strong><br>";
        foreach ($recentLogs as $log) {
            $status = $log['success'] ? '<span class="success">Success</span>' : '<span class="error">Failed</span>';
            echo "- " . htmlspecialchars($log['email']) . " from " . htmlspecialchars($log['ip_address']) . " - $status (" . $log['created_at'] . ")<br>";
        }
    }
    
    // Test 8: Active reset tokens
    echo "<h2>8. Active Reset Tokens</h2>";
    $tokenStmt = $pdo->query("
        SELECT COUNT(*) 
        FROM password_reset_tokens 
        WHERE expires_at > NOW()
    ");
    $activeTokens = $tokenStmt->fetchColumn();
    
    echo "Active reset tokens: $activeTokens<br>";
    
    if ($activeTokens > 0) {
        $tokenDetailsStmt = $pdo->query("
            SELECT prt.token, prt.expires_at, u.username, u.email
            FROM password_reset_tokens prt
            JOIN users u ON prt.user_id = u.id
            WHERE prt.expires_at > NOW()
            ORDER BY prt.created_at DESC
            LIMIT 5
        ");
        $tokenDetails = $tokenDetailsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<strong>Active tokens:</strong><br>";
        foreach ($tokenDetails as $token) {
            echo "- " . htmlspecialchars($token['username']) . " (" . htmlspecialchars($token['email']) . ") - Expires: " . $token['expires_at'] . "<br>";
        }
    }
    
    echo "<h2>Summary</h2>";
    echo "<p><strong>Password Reset System Status:</strong></p>";
    echo "<ul>";
    echo "<li>Database tables: " . (count($tables) === 3 ? '<span class="success">✅ Ready</span>' : '<span class="error">❌ Missing tables</span>') . "</li>";
    echo "<li>Email configuration: " . (ENABLE_EMAIL_NOTIFICATIONS ? '<span class="success">✅ Enabled</span>' : '<span class="error">❌ Disabled</span>') . "</li>";
    echo "<li>Rate limiting: " . ($attempts === 3 ? '<span class="success">✅ Working</span>' : '<span class="error">❌ Not working</span>') . "</li>";
    echo "<li>Test users: " . ($usersWithEmail > 0 ? '<span class="success">✅ Available</span>' : '<span class="error">❌ None found</span>') . "</li>";
    echo "</ul>";
    
    echo "<h2>Test URLs</h2>";
    echo "<ul>";
    echo "<li><a href='forgot_password.html'>Password Reset Request Form</a></li>";
    echo "<li><a href='view_users.php'>Admin Panel (View Users)</a></li>";
    echo "<li><a href='export_users.php?type=password_resets'>Export Password Reset Logs</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</span><br>";
}
?>
