<?php
/**
 * Test Password Reset Functionality
 */

require_once 'config.php';
require_once 'functions.php';

echo "<h1>Test Password Reset Functionality</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// Test 1: Check Emby connection
echo "<h2>1. Testing Emby Connection</h2>";
if (testEmbyConnection()) {
    echo "<span class='success'>✅ Emby server connection successful</span><br>";
} else {
    echo "<span class='error'>❌ Emby server connection failed</span><br>";
    exit;
}

// Test 2: Create a test user first
echo "<h2>2. Creating Test User</h2>";
$testUser = [
    'username' => 'resettest_' . time(),
    'password' => 'originalpass123',
    'email' => '<EMAIL>',
    'fullname' => 'Reset Test User'
];

echo "Creating user: " . htmlspecialchars($testUser['username']) . "<br>";
echo "Original password: " . htmlspecialchars($testUser['password']) . "<br>";

$createResult = createEmbyUser($testUser);

if (!$createResult['success']) {
    echo "<span class='error'>❌ Failed to create test user: " . htmlspecialchars($createResult['error']) . "</span><br>";
    exit;
}

$embyUserId = $createResult['user_id'];
echo "<span class='success'>✅ Test user created successfully</span><br>";
echo "Emby User ID: " . htmlspecialchars($embyUserId) . "<br>";

// Test 3: Verify original password works
echo "<h2>3. Testing Original Password</h2>";
$authResult1 = testEmbyAuthentication($testUser['username'], $testUser['password']);

if ($authResult1['success']) {
    echo "<span class='success'>✅ Original password authentication successful</span><br>";
} else {
    echo "<span class='error'>❌ Original password authentication failed: " . htmlspecialchars($authResult1['error']) . "</span><br>";
}

// Test 4: Test different password reset methods
echo "<h2>4. Testing Password Reset Methods</h2>";

$newPassword = 'newpass456';
echo "New password: " . htmlspecialchars($newPassword) . "<br><br>";

// Method 1: Direct password update
echo "<h3>Method 1: Direct Password Update</h3>";
$method1Result = updateEmbyUserPassword($embyUserId, $newPassword);

if ($method1Result['success']) {
    echo "<span class='success'>✅ Method 1: Password update successful</span><br>";
    
    // Test authentication with new password
    $authResult2 = testEmbyAuthentication($testUser['username'], $newPassword);
    
    if ($authResult2['success']) {
        echo "<span class='success'>✅ Method 1: New password authentication successful</span><br>";
    } else {
        echo "<span class='error'>❌ Method 1: New password authentication failed: " . htmlspecialchars($authResult2['error']) . "</span><br>";
        
        // Try Method 2 if Method 1 auth fails
        echo "<h3>Method 2: Reset then Set Password</h3>";
        
        // Reset to empty first
        $resetResult = resetEmbyUserPasswordToEmpty($embyUserId);
        
        if ($resetResult['success']) {
            echo "<span class='success'>✅ Method 2: Password reset to empty successful</span><br>";
            
            // Set new password
            $setResult = setEmbyUserPassword($embyUserId, $newPassword);
            
            if ($setResult['success']) {
                echo "<span class='success'>✅ Method 2: New password set successful</span><br>";
                
                // Test authentication
                $authResult3 = testEmbyAuthentication($testUser['username'], $newPassword);
                
                if ($authResult3['success']) {
                    echo "<span class='success'>✅ Method 2: New password authentication successful</span><br>";
                } else {
                    echo "<span class='error'>❌ Method 2: New password authentication failed: " . htmlspecialchars($authResult3['error']) . "</span><br>";
                }
            } else {
                echo "<span class='error'>❌ Method 2: Failed to set new password: " . htmlspecialchars($setResult['error']) . "</span><br>";
            }
        } else {
            echo "<span class='error'>❌ Method 2: Failed to reset password: " . htmlspecialchars($resetResult['error']) . "</span><br>";
        }
    }
} else {
    echo "<span class='error'>❌ Method 1: Password update failed: " . htmlspecialchars($method1Result['error']) . "</span><br>";
}

// Test 5: Test alternative API endpoints
echo "<h2>5. Testing Alternative API Methods</h2>";

// Method 3: Try using different API endpoint
echo "<h3>Method 3: Alternative API Endpoint</h3>";
$altPassword = 'altpass789';

$altResult = makeEmbyApiRequest('Users/' . $embyUserId . '/Password', 'POST', [
    'Id' => $embyUserId,
    'CurrentPassword' => '',
    'NewPassword' => $altPassword
]);

if ($altResult['success']) {
    echo "<span class='success'>✅ Method 3: Alternative API successful</span><br>";
    
    $authResult4 = testEmbyAuthentication($testUser['username'], $altPassword);
    
    if ($authResult4['success']) {
        echo "<span class='success'>✅ Method 3: Authentication successful</span><br>";
    } else {
        echo "<span class='error'>❌ Method 3: Authentication failed: " . htmlspecialchars($authResult4['error']) . "</span><br>";
    }
} else {
    echo "<span class='error'>❌ Method 3: Alternative API failed: " . htmlspecialchars($altResult['data']['Message'] ?? 'Unknown error') . "</span><br>";
}

// Test 6: Get user information to check password status
echo "<h2>6. User Information Check</h2>";
$userInfoResult = makeEmbyApiRequest('Users/' . $embyUserId);

if ($userInfoResult['success']) {
    $userInfo = $userInfoResult['data'];
    echo "<span class='success'>✅ User information retrieved</span><br>";
    echo "Name: " . htmlspecialchars($userInfo['Name']) . "<br>";
    echo "Has Password: " . (isset($userInfo['HasPassword']) ? ($userInfo['HasPassword'] ? 'Yes' : 'No') : 'Unknown') . "<br>";
    echo "Has Configured Password: " . (isset($userInfo['HasConfiguredPassword']) ? ($userInfo['HasConfiguredPassword'] ? 'Yes' : 'No') : 'Unknown') . "<br>";
} else {
    echo "<span class='error'>❌ Failed to get user information</span><br>";
}

// Cleanup: Delete test user
echo "<h2>7. Cleanup</h2>";
$deleteResult = deleteEmbyUser($embyUserId);

if ($deleteResult) {
    echo "<span class='success'>✅ Test user deleted successfully</span><br>";
} else {
    echo "<span class='error'>❌ Failed to delete test user</span><br>";
}

echo "<h2>Test Summary</h2>";
echo "<p>This test checks various methods for resetting user passwords in Emby.</p>";
echo "<p>If all methods fail, there might be an issue with the Emby API or server configuration.</p>";

/**
 * Test Emby authentication
 */
function testEmbyAuthentication($username, $password) {
    $endpoint = 'Users/AuthenticateByName';
    
    $data = [
        'Username' => $username,
        'Pw' => $password
    ];
    
    $result = makeEmbyApiRequest($endpoint, 'POST', $data);
    
    if ($result['success']) {
        return [
            'success' => true,
            'token' => $result['data']['AccessToken'] ?? '',
            'user_id' => $result['data']['User']['Id'] ?? ''
        ];
    } else {
        return [
            'success' => false,
            'error' => $result['data']['Message'] ?? 'Authentication failed'
        ];
    }
}
?>
