<?php
/**
 * Test User Deletion Functionality
 */

require_once 'config.php';
require_once 'functions.php';

echo "<h1>Test User Deletion Functionality</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// Test 1: Check Emby connection
echo "<h2>1. Testing Emby Connection</h2>";
if (testEmbyConnection()) {
    echo "<span class='success'>✅ Emby server connection successful</span><br>";
} else {
    echo "<span class='error'>❌ Emby server connection failed</span><br>";
    exit;
}

// Test 2: Create test users
echo "<h2>2. Creating Test Users</h2>";

$testUsers = [
    [
        'username' => 'deletetest1_' . time(),
        'password' => 'testpass123',
        'email' => '<EMAIL>',
        'fullname' => 'Delete Test User 1'
    ],
    [
        'username' => 'deletetest2_' . time(),
        'password' => 'testpass456',
        'email' => '<EMAIL>',
        'fullname' => 'Delete Test User 2'
    ]
];

$createdUsers = [];

foreach ($testUsers as $index => $testUser) {
    echo "<h3>Creating User " . ($index + 1) . ": " . htmlspecialchars($testUser['username']) . "</h3>";
    
    $createResult = createEmbyUser($testUser);
    
    if ($createResult['success']) {
        echo "<span class='success'>✅ User created successfully</span><br>";
        echo "Emby User ID: " . htmlspecialchars($createResult['user_id']) . "<br>";
        
        // Save to database
        $dbResult = saveUserToDatabase($testUser, $createResult['user_id']);
        
        if ($dbResult) {
            echo "<span class='success'>✅ User saved to database</span><br>";
            
            // Get database user ID
            $dbUser = getUserFromDatabase($testUser['username']);
            if ($dbUser) {
                $createdUsers[] = [
                    'db_id' => $dbUser['id'],
                    'emby_id' => $createResult['user_id'],
                    'username' => $testUser['username'],
                    'password' => $testUser['password']
                ];
                echo "Database User ID: " . htmlspecialchars($dbUser['id']) . "<br>";
            }
        } else {
            echo "<span class='error'>❌ Failed to save user to database</span><br>";
        }
    } else {
        echo "<span class='error'>❌ Failed to create user: " . htmlspecialchars($createResult['error']) . "</span><br>";
    }
    echo "<br>";
}

if (empty($createdUsers)) {
    echo "<span class='error'>❌ No users created successfully. Cannot proceed with deletion tests.</span><br>";
    exit;
}

// Test 3: Verify users exist in Emby
echo "<h2>3. Verifying Users Exist in Emby</h2>";

foreach ($createdUsers as $user) {
    echo "<h3>Checking User: " . htmlspecialchars($user['username']) . "</h3>";
    
    // Check if user exists in Emby
    $userInfoResult = makeEmbyApiRequest('Users/' . $user['emby_id']);
    
    if ($userInfoResult['success']) {
        echo "<span class='success'>✅ User exists in Emby</span><br>";
        echo "Name: " . htmlspecialchars($userInfoResult['data']['Name']) . "<br>";
    } else {
        echo "<span class='error'>❌ User not found in Emby</span><br>";
    }
    
    // Test authentication
    $authResult = testEmbyAuthentication($user['username'], $user['password']);
    
    if ($authResult['success']) {
        echo "<span class='success'>✅ User can authenticate</span><br>";
    } else {
        echo "<span class='error'>❌ User authentication failed: " . htmlspecialchars($authResult['error']) . "</span><br>";
    }
    echo "<br>";
}

// Test 4: Test deletion process
echo "<h2>4. Testing User Deletion</h2>";

foreach ($createdUsers as $index => $user) {
    echo "<h3>Deleting User " . ($index + 1) . ": " . htmlspecialchars($user['username']) . "</h3>";
    
    // Delete user using the deleteUser function
    $deleteResult = deleteUser($user['db_id']);
    
    if ($deleteResult) {
        echo "<span class='success'>✅ User deletion function returned success</span><br>";
        
        // Verify user is deleted from database
        $dbCheck = getUserFromDatabase($user['username']);
        
        if (!$dbCheck) {
            echo "<span class='success'>✅ User deleted from database</span><br>";
        } else {
            echo "<span class='error'>❌ User still exists in database</span><br>";
        }
        
        // Verify user is deleted from Emby
        $embyCheck = makeEmbyApiRequest('Users/' . $user['emby_id']);
        
        if (!$embyCheck['success'] && $embyCheck['http_code'] === 404) {
            echo "<span class='success'>✅ User deleted from Emby (404 Not Found)</span><br>";
        } else if (!$embyCheck['success']) {
            echo "<span class='success'>✅ User deleted from Emby (API error: " . $embyCheck['http_code'] . ")</span><br>";
        } else {
            echo "<span class='error'>❌ User still exists in Emby</span><br>";
            echo "User data: " . htmlspecialchars(json_encode($embyCheck['data'])) . "<br>";
        }
        
        // Try to authenticate (should fail)
        $authCheck = testEmbyAuthentication($user['username'], $user['password']);
        
        if (!$authCheck['success']) {
            echo "<span class='success'>✅ User authentication properly fails after deletion</span><br>";
        } else {
            echo "<span class='error'>❌ User can still authenticate after deletion</span><br>";
        }
        
    } else {
        echo "<span class='error'>❌ User deletion function failed</span><br>";
        
        // Manual cleanup if deletion failed
        echo "Attempting manual cleanup...<br>";
        
        // Delete from Emby manually
        $manualEmbyDelete = deleteEmbyUser($user['emby_id']);
        if ($manualEmbyDelete) {
            echo "<span class='success'>✅ Manual Emby deletion successful</span><br>";
        } else {
            echo "<span class='error'>❌ Manual Emby deletion failed</span><br>";
        }
    }
    echo "<br>";
}

echo "<h2>5. Test Summary</h2>";
echo "<p>This test verifies that user deletion works correctly in both the database and Emby server.</p>";
echo "<p>Key points:</p>";
echo "<ul>";
echo "<li>Users should be deleted from both database and Emby</li>";
echo "<li>Deleted users should not be able to authenticate</li>";
echo "<li>API calls for deleted users should return 404</li>";
echo "<li>Error handling should work properly</li>";
echo "</ul>";

/**
 * Test Emby authentication
 */
function testEmbyAuthentication($username, $password) {
    $endpoint = 'Users/AuthenticateByName';
    
    $data = [
        'Username' => $username,
        'Pw' => $password
    ];
    
    $result = makeEmbyApiRequest($endpoint, 'POST', $data);
    
    if ($result['success']) {
        return [
            'success' => true,
            'token' => $result['data']['AccessToken'] ?? '',
            'user_id' => $result['data']['User']['Id'] ?? ''
        ];
    } else {
        return [
            'success' => false,
            'error' => $result['data']['Message'] ?? 'Authentication failed'
        ];
    }
}
?>
