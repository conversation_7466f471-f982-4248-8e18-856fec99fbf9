# Emby Server User Registration System

A PHP-based user registration system that integrates with Emby Server API to allow users to create accounts directly through a web interface.

## Features

- 🎯 **Easy Registration**: User-friendly web form for account creation
- 🔐 **Secure**: CSRF protection, input validation, and rate limiting
- 🎨 **Modern UI**: Responsive Bootstrap design with custom styling
- 📧 **Email Notifications**: Optional email notifications for new registrations
- 📊 **Logging**: Optional database logging of registration attempts
- ⚡ **Real-time Validation**: Username availability checking and form validation
- 🛡️ **Rate Limiting**: Prevents spam registrations
- 📱 **Mobile Friendly**: Responsive design works on all devices

## Requirements

- PHP 7.4 or higher
- cURL extension enabled
- Emby Server with API access
- Web server (Apache/Nginx)
- MySQL database (optional, for logging)

## Installation

### 1. Download and Extract

Extract all files to your web server directory (e.g., `/var/www/html/register_emby/` or `C:\xampp\htdocs\register_emby\`)

### 2. Configure Emby Server

1. Open your Emby Server dashboard
2. Go to **Settings** → **API Keys**
3. Create a new API key for the registration system
4. Note down the API key and your server URL

### 3. Configure the System

Edit `config.php` and update the following settings:

```php
// Emby Server Configuration
define('EMBY_SERVER_URL', 'http://your-emby-server:8096'); // Your Emby server URL
define('EMBY_API_KEY', 'your-api-key-here'); // Your API key from step 2
```

### 4. Optional: Database Setup

If you want to enable logging:

1. Create a MySQL database
2. Update database settings in `config.php`:
   ```php
   define('ENABLE_DATABASE_LOGGING', true);
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'emby_registration');
   define('DB_USERNAME', 'your-db-user');
   define('DB_PASSWORD', 'your-db-password');
   ```
3. Run the database setup:
   ```bash
   php setup_database.php
   ```

### 5. Optional: Email Notifications

To enable email notifications, update `config.php`:

```php
define('ENABLE_EMAIL_NOTIFICATIONS', true);
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');
```

### 6. Set Permissions

Make sure the web server can read all files:

```bash
chmod -R 644 /path/to/register_emby/
chmod 755 /path/to/register_emby/
```

## Usage

1. Open your web browser and navigate to the registration system
2. Fill out the registration form:
   - **Username**: Will be used to log into Emby (required)
   - **Full Name**: Display name in Emby (optional)
   - **Email**: For notifications and password recovery (optional)
   - **Password**: Account password (required)
3. Click "Create Account"
4. If successful, the user can now log into Emby with their credentials

## File Structure

```
register_emby/
├── index.html              # Main registration form
├── register.php            # Registration handler
├── config.php              # Configuration settings
├── functions.php           # Utility functions
├── style.css               # Custom styles
├── script.js               # Client-side JavaScript
├── get_csrf_token.php      # CSRF token generator
├── check_server.php        # Server status checker
├── check_username.php      # Username availability checker
├── setup_database.php      # Database setup script
└── README.md               # This file
```

## Configuration Options

### Security Settings

```php
// Enable/disable registration
define('ALLOW_REGISTRATION', true);

// Rate limiting
define('ENABLE_RATE_LIMITING', true);
define('MAX_REGISTRATIONS_PER_IP', 5); // Per hour

// Password requirements
define('MIN_PASSWORD_LENGTH', 6);
define('MAX_PASSWORD_LENGTH', 50);
```

### User Permissions

The system applies default permissions to new users. You can customize these in `config.php`:

```php
define('DEFAULT_USER_POLICY', [
    'IsAdministrator' => false,
    'EnableMediaPlayback' => true,
    'EnableLiveTvAccess' => true,
    // ... more options
]);
```

## Troubleshooting

### Common Issues

1. **"Unable to connect to Emby server"**
   - Check if `EMBY_SERVER_URL` is correct
   - Verify Emby server is running and accessible
   - Check firewall settings

2. **"Invalid API key"**
   - Verify the API key in `config.php`
   - Make sure the API key has proper permissions

3. **"Username already exists"**
   - The username is already taken in Emby
   - Try a different username

4. **Registration form not loading**
   - Check PHP error logs
   - Verify all files are uploaded correctly
   - Check file permissions

### Debug Mode

To enable debug mode, edit `config.php`:

```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### Logs

- PHP errors: Check your web server error logs
- Registration attempts: If database logging is enabled, check the `registration_log` table

## Security Considerations

- Always use HTTPS in production
- Keep your API key secure
- Regularly update PHP and dependencies
- Monitor registration logs for suspicious activity
- Consider implementing CAPTCHA for additional security

## Customization

### Styling

Edit `style.css` to customize the appearance:
- Colors and gradients
- Fonts and typography
- Layout and spacing

### Form Fields

To add/remove form fields:
1. Update `index.html`
2. Modify validation in `script.js`
3. Update `functions.php` validation
4. Adjust `register.php` processing

### Email Templates

Customize email templates in `register.php`:
- Welcome email content
- Admin notification format

## API Endpoints Used

The system uses these Emby API endpoints:

- `GET /emby/System/Info` - Check server status
- `GET /emby/Users` - Check existing users
- `POST /emby/Users/<USER>
- `POST /emby/Users/<USER>/Policy` - Set user permissions

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Emby API documentation
3. Check PHP error logs
4. Verify configuration settings

## Contributing

Contributions are welcome! Please:
1. Test your changes thoroughly
2. Follow existing code style
3. Update documentation as needed
4. Consider security implications
