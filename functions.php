<?php
/**
 * Emby Server Registration System - Utility Functions
 */

require_once 'config.php';

/**
 * Make HTTP request to Emby API
 */
function makeEmbyApiRequest($endpoint, $method = 'GET', $data = null) {
    $url = EMBY_SERVER_URL . '/emby/' . ltrim($endpoint, '/');
    
    $headers = [
        'X-Emby-Token: ' . EMBY_API_KEY,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    } elseif ($method === 'PUT') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['success' => false, 'error' => 'Connection error: ' . $error];
    }
    
    $decodedResponse = json_decode($response, true);
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => $decodedResponse,
        'raw_response' => $response
    ];
}

/**
 * Check if username already exists in Emby
 */
function checkUsernameExists($username) {
    $result = makeEmbyApiRequest('Users');
    
    if (!$result['success']) {
        return ['exists' => false, 'error' => 'Unable to check username availability'];
    }
    
    if (isset($result['data']) && is_array($result['data'])) {
        foreach ($result['data'] as $user) {
            if (isset($user['Name']) && strtolower($user['Name']) === strtolower($username)) {
                return ['exists' => true];
            }
        }
    }
    
    return ['exists' => false];
}

/**
 * Create new user in Emby
 */
function createEmbyUser($userData) {
    // First create user without password
    $userPayload = [
        'Name' => $userData['username']
    ];

    // Add optional fields if provided
    if (!empty($userData['email'])) {
        $userPayload['Email'] = $userData['email'];
    }

    if (!empty($userData['fullname'])) {
        $userPayload['FullName'] = $userData['fullname'];
    }

    $result = makeEmbyApiRequest('Users/New', 'POST', $userPayload);
    
    if (!$result['success']) {
        return [
            'success' => false,
            'error' => 'Failed to create user: ' . ($result['data']['Message'] ?? 'Unknown error'),
            'http_code' => $result['http_code']
        ];
    }
    
    // If user creation was successful, set password and apply policy
    if (isset($result['data']['Id'])) {
        $userId = $result['data']['Id'];

        // Set password using separate API call
        $passwordResult = setEmbyUserPassword($userId, $userData['password']);
        if (!$passwordResult['success']) {
            // If password setting fails, delete the user and return error
            deleteEmbyUser($userId);
            return [
                'success' => false,
                'error' => 'Failed to set password: ' . $passwordResult['error'],
                'http_code' => $passwordResult['http_code'] ?? 500
            ];
        }

        // Merge default policy with hidden setting
        $policy = DEFAULT_USER_POLICY;
        $policy['IsHidden'] = true;  // Hide this user from login screens on the local network

        applyUserPolicy($userId, $policy);
    }
    
    return [
        'success' => true,
        'user_id' => $result['data']['Id'] ?? null,
        'data' => $result['data']
    ];
}

/**
 * Set password for newly created Emby user
 */
function setEmbyUserPassword($userId, $password) {
    $endpoint = 'Users/' . $userId . '/Password';

    $data = [
        'CurrentPw' => '',  // Empty for new users
        'NewPw' => $password,
        'ResetPassword' => false
    ];

    $result = makeEmbyApiRequest($endpoint, 'POST', $data);

    if (!$result['success']) {
        return [
            'success' => false,
            'error' => 'Failed to set password: ' . ($result['data']['Message'] ?? 'Unknown error'),
            'http_code' => $result['http_code']
        ];
    }

    return ['success' => true];
}

/**
 * Apply user policy to newly created user
 */
function applyUserPolicy($userId, $policy) {
    $endpoint = 'Users/' . $userId . '/Policy';
    $result = makeEmbyApiRequest($endpoint, 'POST', $policy);

    return $result['success'];
}

/**
 * Validate input data
 */
function validateInput($data) {
    $errors = [];
    
    // Validate username
    if (empty($data['username'])) {
        $errors[] = 'Username is required';
    } elseif (strlen($data['username']) < MIN_USERNAME_LENGTH) {
        $errors[] = ERROR_MESSAGES['USERNAME_TOO_SHORT'];
    } elseif (strlen($data['username']) > MAX_USERNAME_LENGTH) {
        $errors[] = ERROR_MESSAGES['USERNAME_TOO_LONG'];
    } elseif (!preg_match('/^[a-zA-Z0-9_-]+$/', $data['username'])) {
        $errors[] = 'Username can only contain letters, numbers, underscores, and hyphens';
    }
    
    // Validate password
    if (empty($data['password'])) {
        $errors[] = 'Password is required';
    } elseif (strlen($data['password']) < MIN_PASSWORD_LENGTH) {
        $errors[] = ERROR_MESSAGES['PASSWORD_TOO_SHORT'];
    } elseif (strlen($data['password']) > MAX_PASSWORD_LENGTH) {
        $errors[] = ERROR_MESSAGES['PASSWORD_TOO_LONG'];
    }
    
    // Validate password confirmation
    if (empty($data['confirm_password'])) {
        $errors[] = 'Password confirmation is required';
    } elseif ($data['password'] !== $data['confirm_password']) {
        $errors[] = 'Passwords do not match';
    }
    
    // Validate email (optional but if provided must be valid)
    if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = ERROR_MESSAGES['INVALID_EMAIL'];
    }
    
    return $errors;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    return [
        'username' => trim(strip_tags($data['username'] ?? '')),
        'password' => $data['password'] ?? '',
        'confirm_password' => $data['confirm_password'] ?? '',
        'email' => trim(strip_tags($data['email'] ?? '')),
        'fullname' => trim(strip_tags($data['fullname'] ?? '')),
        'csrf_token' => $data['csrf_token'] ?? ''
    ];
}

/**
 * Check rate limiting
 */
function checkRateLimit($ip) {
    if (!ENABLE_RATE_LIMITING) {
        return true;
    }
    
    $sessionKey = 'rate_limit_' . md5($ip);
    $attempts = $_SESSION[$sessionKey] ?? [];
    $now = time();
    
    // Remove attempts older than 1 hour
    $attempts = array_filter($attempts, function($timestamp) use ($now) {
        return ($now - $timestamp) < 3600;
    });
    
    // Check if limit exceeded
    if (count($attempts) >= MAX_REGISTRATIONS_PER_IP) {
        return false;
    }
    
    // Add current attempt
    $attempts[] = $now;
    $_SESSION[$sessionKey] = $attempts;
    
    return true;
}

/**
 * Save user to database
 */
function saveUserToDatabase($userData, $embyUserId = null) {
    if (!ENABLE_DATABASE_LOGGING) {
        return true;
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        // Hash password for storage
        $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, full_name, emby_user_id, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");

        $result = $stmt->execute([
            $userData['username'],
            $hashedPassword,
            $userData['email'],
            $userData['fullname'],
            $embyUserId
        ]);

        return $result;
    } catch (Exception $e) {
        error_log("Database user save failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if username exists in database
 */
function checkUsernameInDatabase($username) {
    if (!ENABLE_DATABASE_LOGGING) {
        return false;
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);

        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("Database username check failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user from database
 */
function getUserFromDatabase($username) {
    if (!ENABLE_DATABASE_LOGGING) {
        return null;
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        $stmt = $pdo->prepare("
            SELECT id, username, email, full_name, emby_user_id, is_active, created_at
            FROM users
            WHERE username = ? AND is_active = 1
        ");
        $stmt->execute([$username]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Database user fetch failed: " . $e->getMessage());
        return null;
    }
}

/**
 * Log registration attempt
 */
function logRegistration($username, $email, $ip, $success, $error = null) {
    if (!ENABLE_DATABASE_LOGGING) {
        return;
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        $stmt = $pdo->prepare("
            INSERT INTO registration_log (username, email, ip_address, success, error_message, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");

        $stmt->execute([$username, $email, $ip, $success ? 1 : 0, $error]);
    } catch (Exception $e) {
        // Log error but don't fail the registration
        error_log("Database logging failed: " . $e->getMessage());
    }
}

/**
 * Send email notification
 */
function sendEmailNotification($to, $subject, $message) {
    if (!ENABLE_EMAIL_NOTIFICATIONS) {
        return true;
    }
    
    // Simple mail function - you might want to use PHPMailer for production
    $headers = [
        'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
        'Reply-To: ' . FROM_EMAIL,
        'Content-Type: text/html; charset=UTF-8',
        'X-Mailer: PHP/' . phpversion()
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Test Emby server connection
 */
function testEmbyConnection() {
    $result = makeEmbyApiRequest('System/Info');
    return $result['success'];
}





/**
 * Update user password in database and Emby
 */
function updateUserPassword($userId, $newPassword) {
    if (!ENABLE_DATABASE_LOGGING) {
        return ['success' => false, 'error' => 'Database not enabled'];
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        // Get user data
        $stmt = $pdo->prepare("SELECT username, emby_user_id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }

        // Update password in Emby if we have the Emby user ID
        if (!empty($user['emby_user_id'])) {
            $embyResult = updateEmbyUserPassword($user['emby_user_id'], $newPassword);
            if (!$embyResult['success']) {
                return ['success' => false, 'error' => 'Failed to update password in Emby: ' . $embyResult['error']];
            }
        }

        // Update password in database
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $updateStmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
        $updateResult = $updateStmt->execute([$hashedPassword, $userId]);

        if (!$updateResult) {
            return ['success' => false, 'error' => 'Failed to update password in database'];
        }

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Update user password failed: " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error occurred'];
    }
}

/**
 * Update user password in Emby
 */
function updateEmbyUserPassword($embyUserId, $newPassword) {
    // Method 1: Try using the same method as setEmbyUserPassword
    $endpoint = 'Users/' . $embyUserId . '/Password';

    $data = [
        'CurrentPw' => '',  // Empty for admin reset
        'NewPw' => $newPassword,
        'ResetPassword' => false  // Changed to false like in setEmbyUserPassword
    ];

    $result = makeEmbyApiRequest($endpoint, 'POST', $data);

    if (!$result['success']) {
        // Method 2: Try alternative approach - reset password first, then set new one
        $resetResult = resetEmbyUserPasswordToEmpty($embyUserId);
        if ($resetResult['success']) {
            // Now try to set the new password
            $setResult = setEmbyUserPassword($embyUserId, $newPassword);
            if ($setResult['success']) {
                return ['success' => true];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to set new password after reset: ' . $setResult['error']
                ];
            }
        } else {
            return [
                'success' => false,
                'error' => 'Failed to update password in Emby: ' . ($result['data']['Message'] ?? 'Unknown error')
            ];
        }
    }

    return ['success' => true];
}

/**
 * Reset Emby user password to empty (for admin reset)
 */
function resetEmbyUserPasswordToEmpty($embyUserId) {
    $endpoint = 'Users/' . $embyUserId . '/Password';

    $data = [
        'CurrentPw' => '',
        'NewPw' => '',
        'ResetPassword' => true
    ];

    $result = makeEmbyApiRequest($endpoint, 'POST', $data);

    if (!$result['success']) {
        return [
            'success' => false,
            'error' => 'Failed to reset password: ' . ($result['data']['Message'] ?? 'Unknown error')
        ];
    }

    return ['success' => true];
}



/**
 * Get base URL for reset links
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = dirname($_SERVER['REQUEST_URI'] ?? '');

    return $protocol . '://' . $host . $path;
}

/**
 * Toggle user active status
 */
function toggleUserStatus($userId) {
    if (!ENABLE_DATABASE_LOGGING) {
        return false;
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        $stmt = $pdo->prepare("UPDATE users SET is_active = NOT is_active, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$userId]);
    } catch (Exception $e) {
        error_log("Toggle user status failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete user from database and Emby
 */
function deleteUser($userId) {
    if (!ENABLE_DATABASE_LOGGING) {
        return false;
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        // Get user data first
        $stmt = $pdo->prepare("SELECT username, emby_user_id FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            return false;
        }

        // Delete from Emby first if we have the Emby user ID
        $embyDeleteSuccess = true;
        if (!empty($user['emby_user_id'])) {
            $embyDeleteSuccess = deleteEmbyUser($user['emby_user_id']);
            if (!$embyDeleteSuccess) {
                error_log("Failed to delete user from Emby: " . $user['username'] . " (ID: " . $user['emby_user_id'] . ")");
                // Continue with database deletion even if Emby deletion fails
            }
        }

        // Delete from database
        $deleteStmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $dbDeleteSuccess = $deleteStmt->execute([$userId]);

        if ($dbDeleteSuccess) {
            if ($embyDeleteSuccess) {
                error_log("User successfully deleted from both database and Emby: " . $user['username']);
            } else {
                error_log("User deleted from database but failed to delete from Emby: " . $user['username']);
            }
            return true;
        } else {
            error_log("Failed to delete user from database: " . $user['username']);
            return false;
        }

    } catch (Exception $e) {
        error_log("Delete user failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete user from Emby
 */
function deleteEmbyUser($embyUserId) {
    $endpoint = 'Users/' . $embyUserId;
    $result = makeEmbyApiRequest($endpoint, 'DELETE');

    if (!$result['success']) {
        $errorMsg = $result['data']['Message'] ?? 'Unknown error';
        error_log("Emby user deletion failed for ID $embyUserId: " . $errorMsg . " (HTTP: " . $result['http_code'] . ")");

        // If user doesn't exist in Emby (404), consider it a success
        if ($result['http_code'] === 404) {
            error_log("Emby user ID $embyUserId not found (404) - considering deletion successful");
            return true;
        }

        return false;
    }

    error_log("Emby user ID $embyUserId deleted successfully");
    return true;
}

/**
 * Reset user password (admin function)
 */
function resetUserPassword($userId, $newPassword) {
    if (!ENABLE_DATABASE_LOGGING) {
        return ['success' => false, 'error' => 'Database not enabled'];
    }

    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        // Get user data
        $stmt = $pdo->prepare("SELECT username, emby_user_id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }

        // Update password in Emby if we have the Emby user ID
        if (!empty($user['emby_user_id'])) {
            $embyResult = updateEmbyUserPassword($user['emby_user_id'], $newPassword);
            if (!$embyResult['success']) {
                return ['success' => false, 'error' => 'Failed to update password in Emby: ' . $embyResult['error']];
            }
        }

        // Update password in database
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $updateStmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
        $updateResult = $updateStmt->execute([$hashedPassword, $userId]);

        if (!$updateResult) {
            return ['success' => false, 'error' => 'Failed to update password in database'];
        }

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Reset user password failed: " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error occurred'];
    }
}
?>
