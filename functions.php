<?php
/**
 * Emby Server Registration System - Utility Functions
 */

require_once 'config.php';

/**
 * Make HTTP request to Emby API
 */
function makeEmbyApiRequest($endpoint, $method = 'GET', $data = null) {
    $url = EMBY_SERVER_URL . '/emby/' . ltrim($endpoint, '/');
    
    $headers = [
        'X-Emby-Token: ' . EMBY_API_KEY,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    } elseif ($method === 'PUT') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['success' => false, 'error' => 'Connection error: ' . $error];
    }
    
    $decodedResponse = json_decode($response, true);
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => $decodedResponse,
        'raw_response' => $response
    ];
}

/**
 * Check if username already exists in Emby
 */
function checkUsernameExists($username) {
    $result = makeEmbyApiRequest('Users');
    
    if (!$result['success']) {
        return ['exists' => false, 'error' => 'Unable to check username availability'];
    }
    
    if (isset($result['data']) && is_array($result['data'])) {
        foreach ($result['data'] as $user) {
            if (isset($user['Name']) && strtolower($user['Name']) === strtolower($username)) {
                return ['exists' => true];
            }
        }
    }
    
    return ['exists' => false];
}

/**
 * Create new user in Emby
 */
function createEmbyUser($userData) {
    $userPayload = [
        'Name' => $userData['username'],
        'Password' => $userData['password']
    ];
    
    // Add optional fields if provided
    if (!empty($userData['email'])) {
        $userPayload['Email'] = $userData['email'];
    }
    
    if (!empty($userData['fullname'])) {
        $userPayload['FullName'] = $userData['fullname'];
    }
    
    $result = makeEmbyApiRequest('Users/New', 'POST', $userPayload);
    
    if (!$result['success']) {
        return [
            'success' => false,
            'error' => 'Failed to create user: ' . ($result['data']['Message'] ?? 'Unknown error'),
            'http_code' => $result['http_code']
        ];
    }
    
    // If user creation was successful, apply default policy
    if (isset($result['data']['Id'])) {
        $userId = $result['data']['Id'];
        applyUserPolicy($userId, DEFAULT_USER_POLICY);
    }
    
    return [
        'success' => true,
        'user_id' => $result['data']['Id'] ?? null,
        'data' => $result['data']
    ];
}

/**
 * Apply user policy to newly created user
 */
function applyUserPolicy($userId, $policy) {
    $endpoint = 'Users/' . $userId . '/Policy';
    $result = makeEmbyApiRequest($endpoint, 'POST', $policy);
    
    return $result['success'];
}

/**
 * Validate input data
 */
function validateInput($data) {
    $errors = [];
    
    // Validate username
    if (empty($data['username'])) {
        $errors[] = 'Username is required';
    } elseif (strlen($data['username']) < MIN_USERNAME_LENGTH) {
        $errors[] = ERROR_MESSAGES['USERNAME_TOO_SHORT'];
    } elseif (strlen($data['username']) > MAX_USERNAME_LENGTH) {
        $errors[] = ERROR_MESSAGES['USERNAME_TOO_LONG'];
    } elseif (!preg_match('/^[a-zA-Z0-9_-]+$/', $data['username'])) {
        $errors[] = 'Username can only contain letters, numbers, underscores, and hyphens';
    }
    
    // Validate password
    if (empty($data['password'])) {
        $errors[] = 'Password is required';
    } elseif (strlen($data['password']) < MIN_PASSWORD_LENGTH) {
        $errors[] = ERROR_MESSAGES['PASSWORD_TOO_SHORT'];
    } elseif (strlen($data['password']) > MAX_PASSWORD_LENGTH) {
        $errors[] = ERROR_MESSAGES['PASSWORD_TOO_LONG'];
    }
    
    // Validate password confirmation
    if (empty($data['confirm_password'])) {
        $errors[] = 'Password confirmation is required';
    } elseif ($data['password'] !== $data['confirm_password']) {
        $errors[] = 'Passwords do not match';
    }
    
    // Validate email (optional but if provided must be valid)
    if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = ERROR_MESSAGES['INVALID_EMAIL'];
    }
    
    return $errors;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    return [
        'username' => trim(strip_tags($data['username'] ?? '')),
        'password' => $data['password'] ?? '',
        'confirm_password' => $data['confirm_password'] ?? '',
        'email' => trim(strip_tags($data['email'] ?? '')),
        'fullname' => trim(strip_tags($data['fullname'] ?? '')),
        'csrf_token' => $data['csrf_token'] ?? ''
    ];
}

/**
 * Check rate limiting
 */
function checkRateLimit($ip) {
    if (!ENABLE_RATE_LIMITING) {
        return true;
    }
    
    $sessionKey = 'rate_limit_' . md5($ip);
    $attempts = $_SESSION[$sessionKey] ?? [];
    $now = time();
    
    // Remove attempts older than 1 hour
    $attempts = array_filter($attempts, function($timestamp) use ($now) {
        return ($now - $timestamp) < 3600;
    });
    
    // Check if limit exceeded
    if (count($attempts) >= MAX_REGISTRATIONS_PER_IP) {
        return false;
    }
    
    // Add current attempt
    $attempts[] = $now;
    $_SESSION[$sessionKey] = $attempts;
    
    return true;
}

/**
 * Log registration attempt
 */
function logRegistration($username, $email, $ip, $success, $error = null) {
    if (!ENABLE_DATABASE_LOGGING) {
        return;
    }
    
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        $stmt = $pdo->prepare("
            INSERT INTO registration_log (username, email, ip_address, success, error_message, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$username, $email, $ip, $success ? 1 : 0, $error]);
    } catch (Exception $e) {
        // Log error but don't fail the registration
        error_log("Database logging failed: " . $e->getMessage());
    }
}

/**
 * Send email notification
 */
function sendEmailNotification($to, $subject, $message) {
    if (!ENABLE_EMAIL_NOTIFICATIONS) {
        return true;
    }
    
    // Simple mail function - you might want to use PHPMailer for production
    $headers = [
        'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
        'Reply-To: ' . FROM_EMAIL,
        'Content-Type: text/html; charset=UTF-8',
        'X-Mailer: PHP/' . phpversion()
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Test Emby server connection
 */
function testEmbyConnection() {
    $result = makeEmbyApiRequest('System/Info');
    return $result['success'];
}
?>
