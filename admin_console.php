<?php
/**
 * Admin Console - Emby Registration System
 */

require_once 'config.php';
require_once 'functions.php';

// Simple authentication
$adminPassword = 'Wxmujwsofu@1234'; // Change this!
$isAuthenticated = false;

if (isset($_POST['admin_password'])) {
    if ($_POST['admin_password'] === $adminPassword) {
        $_SESSION['admin_authenticated'] = true;
        $isAuthenticated = true;
    } else {
        $error = 'Invalid password';
    }
}

if (isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated']) {
    $isAuthenticated = true;
}

// Handle logout
if (isset($_GET['logout'])) {
    unset($_SESSION['admin_authenticated']);
    header('Location: admin_console.php');
    exit;
}

// Handle user actions
if ($isAuthenticated && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $userId = $_POST['user_id'] ?? '';
    
    if ($action === 'toggle_status' && $userId) {
        toggleUserStatus($userId);
        $success_message = 'User status updated successfully';
    } elseif ($action === 'delete_user' && $userId) {
        deleteUser($userId);
        $success_message = 'User deleted successfully';
    } elseif ($action === 'reset_password' && $userId) {
        $newPassword = $_POST['new_password'] ?? '';
        if ($newPassword) {
            $result = resetUserPassword($userId, $newPassword);
            if ($result['success']) {
                $success_message = 'Password reset successfully';
            } else {
                $error_message = $result['error'];
            }
        }
    }
}

if (!$isAuthenticated) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Console - Emby Registration</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="style.css" rel="stylesheet">
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="card mt-5">
                        <div class="card-header text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <svg width="40" height="40" viewBox="0 0 100 100" class="me-3">
                                    <circle cx="50" cy="50" r="45" fill="#00a4dc"/>
                                    <text x="50" y="60" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">E</text>
                                </svg>
                                <h4 class="mb-0">Admin Console</h4>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                            <?php endif; ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="admin_password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Admin Password
                                    </label>
                                    <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </form>
                            <div class="text-center mt-3">
                                <a href="index.html" class="btn btn-link">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Registration
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Get statistics and data
try {
    if (ENABLE_DATABASE_LOGGING) {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        // Get users with pagination
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 15;
        $offset = ($page - 1) * $limit;
        
        // Count total users
        $countStmt = $pdo->query("SELECT COUNT(*) FROM users");
        $totalUsers = $countStmt->fetchColumn();
        $totalPages = ceil($totalUsers / $limit);
        
        // Get users
        $stmt = $pdo->prepare("
            SELECT id, username, email, full_name, emby_user_id, is_active, created_at
            FROM users
            ORDER BY created_at DESC
            LIMIT $limit OFFSET $offset
        ");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get statistics
        $statsStmt = $pdo->query("
            SELECT
                COUNT(*) as total_registrations,
                SUM(success) as successful_registrations,
                COUNT(*) - SUM(success) as failed_registrations
            FROM registration_log
        ");
        $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

        // Ensure stats has default values if no data
        if (!$stats) {
            $stats = [
                'total_registrations' => 0,
                'successful_registrations' => 0,
                'failed_registrations' => 0
            ];
        }
        
        // Get recent activity
        $activityStmt = $pdo->query("
            SELECT username, email, ip_address, success, created_at
            FROM registration_log 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $recentActivity = $activityStmt->fetchAll(PDO::FETCH_ASSOC);
        
    } else {
        $totalUsers = 0;
        $totalPages = 0;
        $users = [];
        $stats = ['total_registrations' => 0, 'successful_registrations' => 0, 'failed_registrations' => 0];
        $recentActivity = [];
    }
} catch (Exception $e) {
    $db_error = $e->getMessage();
    $totalUsers = 0;
    $totalPages = 0;
    $users = [];
    $stats = ['total_registrations' => 0, 'successful_registrations' => 0, 'failed_registrations' => 0];
    $recentActivity = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Console - Emby Registration System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <style>
        .stat-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .activity-item {
            border-left: 3px solid #00a4dc;
            padding-left: 15px;
            margin-bottom: 10px;
        }
        .activity-item.failed {
            border-left-color: #dc3545;
        }

        /* Server Status Animations */
        @keyframes pulse {
            0% {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
                transform: scale(1.02);
            }
            100% {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                transform: scale(1);
            }
        }

        @keyframes pulse-offline {
            0% {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            50% {
                box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
            }
            100% {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .spinner-border-sm {
            animation: spin 1s linear infinite;
        }

        /* Server Status Card Specific Styles */
        #serverStatusCard {
            position: relative;
            overflow: hidden;
        }

        #serverStatusCard::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        #serverStatusCard:hover::before {
            left: 100%;
        }

        /* Status-specific styles */
        .status-online {
            background: linear-gradient(135deg, #28a745 0%, #34ce57 100%) !important;
            border: 2px solid #20c997 !important;
        }

        .status-offline {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%) !important;
            border: 2px solid #c82333 !important;
        }

        .status-unknown {
            background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%) !important;
            border: 2px solid #e0a800 !important;
        }

        .status-checking {
            background: linear-gradient(135deg, #6c757d 0%, #95a5a6 100%) !important;
            border: 2px solid #5a6268 !important;
        }

        /* Icon animations */
        .server-icon-online {
            animation: pulse-icon 2s ease-in-out infinite;
        }

        @keyframes pulse-icon {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .stat-card .card-body {
                padding: 1rem;
            }
            .stat-card h3 {
                font-size: 1.5rem;
            }
            .stat-card i {
                font-size: 1.5rem !important;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #00a4dc 0%, #0078a8 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <svg width="30" height="30" viewBox="0 0 100 100" class="me-2">
                    <circle cx="50" cy="50" r="45" fill="white"/>
                    <text x="50" y="60" text-anchor="middle" fill="#00a4dc" font-family="Arial, sans-serif" font-size="32" font-weight="bold">E</text>
                </svg>
                Emby Admin Console
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.html">
                    <i class="fas fa-home me-1"></i>Registration
                </a>
                <a class="nav-link" href="view_users.php">
                    <i class="fas fa-users me-1"></i>Users
                </a>
                <a class="nav-link" href="?logout=1">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($db_error)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-database me-2"></i>Database not available: <?= htmlspecialchars($db_error) ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Dashboard -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3><?= $totalUsers ?></h3>
                        <p class="mb-0">Total Users</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3><?= $stats['successful_registrations'] ?? 0 ?></h3>
                        <p class="mb-0">Successful Registrations</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-2x mb-2"></i>
                        <h3><?= $stats['failed_registrations'] ?? 0 ?></h3>
                        <p class="mb-0">Failed Registrations</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-secondary text-white" id="serverStatusCard">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-server fa-2x me-2" id="serverIcon"></i>
                            <div class="spinner-border spinner-border-sm" role="status" id="serverSpinner">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <h3 id="serverStatus">Checking...</h3>
                        <p class="mb-0">Emby Server</p>
                        <small id="serverUrl" class="text-white-50"></small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Users Management -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-users me-2"></i>User Management</h5>
                        <div>
                            <a href="export_users.php?type=users" class="btn btn-success btn-sm">
                                <i class="fas fa-download me-1"></i>Export
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>No users found</p>
                                <a href="index.html" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Register First User
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Username</th>
                                            <th>Email</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($user['username']) ?></strong>
                                                    <?php if ($user['full_name']): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($user['full_name']) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= htmlspecialchars($user['email'] ?: '-') ?></td>
                                                <td>
                                                    <?php if ($user['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small><?= date('M j, Y', strtotime($user['created_at'])) ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="toggleUserStatus(<?= $user['id'] ?>)">
                                                            <i class="fas fa-toggle-<?= $user['is_active'] ? 'on' : 'off' ?>"></i>
                                                        </button>
                                                        <button class="btn btn-outline-warning" onclick="resetPassword(<?= $user['id'] ?>, '<?= htmlspecialchars($user['username']) ?>')">
                                                            <i class="fas fa-key"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['username']) ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if (isset($totalPages) && $totalPages > 1): ?>
                                <nav>
                                    <ul class="pagination justify-content-center">
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?= $i === ($page ?? 1) ? 'active' : '' ?>">
                                                <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                            </li>
                                        <?php endfor; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentActivity)): ?>
                            <p class="text-muted text-center">No recent activity</p>
                        <?php else: ?>
                            <?php foreach ($recentActivity as $activity): ?>
                                <div class="activity-item <?= ($activity['success'] ?? false) ? '' : 'failed' ?>">
                                    <div class="d-flex justify-content-between">
                                        <strong><?= htmlspecialchars($activity['username'] ?? 'Unknown') ?></strong>
                                        <small><?= date('M j, H:i', strtotime($activity['created_at'] ?? 'now')) ?></small>
                                    </div>
                                    <div class="text-muted small">
                                        <?= htmlspecialchars($activity['email'] ?? 'No email') ?> from <?= htmlspecialchars($activity['ip_address'] ?? 'Unknown IP') ?>
                                    </div>
                                    <div>
                                        <?php if ($activity['success'] ?? false): ?>
                                            <span class="badge bg-success">Success</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Failed</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden Forms for Actions -->
    <form id="actionForm" method="POST" style="display: none;">
        <input type="hidden" name="action" id="actionType">
        <input type="hidden" name="user_id" id="actionUserId">
        <input type="hidden" name="new_password" id="actionPassword">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check server status
        checkServerStatus();

        function checkServerStatus() {
            const statusElement = document.getElementById('serverStatus');
            const cardElement = document.getElementById('serverStatusCard');
            const iconElement = document.getElementById('serverIcon');
            const spinnerElement = document.getElementById('serverSpinner');
            const urlElement = document.getElementById('serverUrl');

            // Show loading state
            spinnerElement.style.display = 'inline-block';
            iconElement.style.display = 'none';
            statusElement.textContent = 'Checking...';
            cardElement.className = 'card stat-card status-checking text-white';

            fetch('check_server.php')
                .then(response => response.json())
                .then(data => {
                    // Hide spinner, show icon
                    spinnerElement.style.display = 'none';
                    iconElement.style.display = 'inline-block';

                    if (data.online) {
                        statusElement.textContent = 'Online';
                        statusElement.style.fontWeight = 'bold';
                        cardElement.className = 'card stat-card status-online text-white';
                        iconElement.className = 'fas fa-check-circle fa-2x me-2 text-white server-icon-online';
                        urlElement.textContent = '<?= EMBY_SERVER_URL ?>';

                        // Add green pulse animation for online
                        cardElement.style.animation = 'pulse 2s ease-in-out infinite';
                    } else {
                        statusElement.textContent = 'Offline';
                        statusElement.style.fontWeight = 'bold';
                        cardElement.className = 'card stat-card status-offline text-white';
                        iconElement.className = 'fas fa-times-circle fa-2x me-2 text-white';
                        urlElement.textContent = 'Connection failed';

                        // Add red pulse animation for offline
                        cardElement.style.animation = 'pulse-offline 2s ease-in-out infinite';
                    }
                })
                .catch(error => {
                    // Hide spinner, show icon
                    spinnerElement.style.display = 'none';
                    iconElement.style.display = 'inline-block';

                    statusElement.textContent = 'Unknown';
                    statusElement.style.fontWeight = 'bold';
                    cardElement.className = 'card stat-card status-unknown text-white';
                    iconElement.className = 'fas fa-question-circle fa-2x me-2 text-white';
                    urlElement.textContent = 'Check failed';
                    cardElement.style.animation = 'none';
                    console.error('Server status check failed:', error);
                });
        }

        // Auto-refresh server status every 30 seconds
        setInterval(checkServerStatus, 30000);

        function toggleUserStatus(userId) {
            if (confirm('Are you sure you want to toggle this user\'s status?')) {
                document.getElementById('actionType').value = 'toggle_status';
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionForm').submit();
            }
        }

        function resetPassword(userId, username) {
            const newPassword = prompt(`Enter new password for ${username}:`);
            if (newPassword && newPassword.length >= 6) {
                document.getElementById('actionType').value = 'reset_password';
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionPassword').value = newPassword;
                document.getElementById('actionForm').submit();
            } else if (newPassword) {
                alert('Password must be at least 6 characters long');
            }
        }

        function deleteUser(userId, username) {
            if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
                document.getElementById('actionType').value = 'delete_user';
                document.getElementById('actionUserId').value = userId;
                document.getElementById('actionForm').submit();
            }
        }
    </script>
</body>
</html>
