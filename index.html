<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emby Server Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg">
                    <div class="card-header text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <svg width="40" height="40" viewBox="0 0 100 100" class="me-3">
                                <circle cx="50" cy="50" r="45" fill="#00a4dc"/>
                                <text x="50" y="60" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">E</text>
                            </svg>
                            <h3 class="mb-0">Emby Server Registration</h3>
                        </div>
                        <p class="mb-0 text-white-50">Create your account to access the media server</p>
                    </div>
                    <div class="card-body">
                        <!-- Alert Messages -->
                        <div id="alertContainer"></div>
                        
                        <!-- Registration Form -->
                        <form id="registrationForm" novalidate>
                            <!-- CSRF Token -->
                            <input type="hidden" id="csrfToken" name="csrf_token" value="">
                            
                            <!-- Username -->
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>Username <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required
                                       minlength="3" maxlength="20" pattern="[a-zA-Z0-9_-]+"
                                       placeholder="Enter your username">
                                <div class="invalid-feedback">
                                    Username must be 3-20 characters long and contain only letters, numbers, underscores, and hyphens.
                                </div>
                                <div class="form-text">
                                    This will be your login username for Emby.
                                </div>
                            </div>
                            
                            <!-- Full Name -->
                            <div class="mb-3">
                                <label for="fullname" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>Full Name
                                </label>
                                <input type="text" class="form-control" id="fullname" name="fullname"
                                       maxlength="100" placeholder="Enter your full name">
                                <div class="form-text">
                                    Optional: Your display name in Emby.
                                </div>
                            </div>
                            
                            <!-- Email -->
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email Address
                                </label>
                                <input type="email" class="form-control" id="email" name="email"
                                       placeholder="Enter your email address">
                                <div class="invalid-feedback">
                                    Please enter a valid email address.
                                </div>
                                <div class="form-text">
                                    Optional: For account notifications and password recovery.
                                </div>
                            </div>
                            
                            <!-- Password -->
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Password <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required
                                           minlength="6" maxlength="50" placeholder="Enter your password">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Password must be at least 6 characters long.
                                </div>
                                <div class="form-text">
                                    Choose a strong password for your account.
                                </div>
                            </div>
                            
                            <!-- Confirm Password -->
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Confirm Password <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required
                                           minlength="6" maxlength="50" placeholder="Confirm your password">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Passwords must match.
                                </div>
                            </div>
                            
                            <!-- Terms and Conditions -->
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms of Service</a> <span class="text-danger">*</span>
                                </label>
                                <div class="invalid-feedback">
                                    You must agree to the terms of service.
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </button>
                            </div>
                        </form>
                        
                        <!-- Login Link -->
                        <div class="text-center mt-3">
                            <p class="mb-0">Already have an account?</p>
                            <a href="#" id="loginLink" class="btn btn-link">
                                <i class="fas fa-sign-in-alt me-1"></i>Login to Emby
                            </a>
                        </div>

                        <!-- Forgot Password Link -->
                        <div class="text-center mt-2">
                            <a href="forgot_password.html" class="btn btn-link btn-sm">
                                <i class="fas fa-key me-1"></i>Forgot Password?
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Server Status -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <small class="text-muted">
                            <i class="fas fa-server me-1"></i>Server Status: 
                            <span id="serverStatus" class="badge bg-secondary">Checking...</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Terms of Service Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="termsModalLabel">Terms of Service</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>1. Acceptance of Terms</h6>
                    <p>By creating an account on this Emby server, you agree to comply with these terms of service.</p>
                    
                    <h6>2. Account Responsibility</h6>
                    <p>You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.</p>
                    
                    <h6>3. Acceptable Use</h6>
                    <p>You agree to use this service only for lawful purposes and in accordance with these terms. You will not:</p>
                    <ul>
                        <li>Share your account credentials with others</li>
                        <li>Attempt to gain unauthorized access to the server</li>
                        <li>Upload or share copyrighted content without permission</li>
                        <li>Use the service for any illegal activities</li>
                    </ul>
                    
                    <h6>4. Privacy</h6>
                    <p>We respect your privacy and will not share your personal information with third parties without your consent.</p>
                    
                    <h6>5. Service Availability</h6>
                    <p>While we strive to maintain high availability, we cannot guarantee uninterrupted service.</p>
                    
                    <h6>6. Account Termination</h6>
                    <p>We reserve the right to terminate accounts that violate these terms of service.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="successModalLabel">
                        <i class="fas fa-check-circle me-2"></i>Registration Successful!
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                    <h4 class="mt-3">Welcome to Emby!</h4>
                    <p class="mb-0">Your account has been created successfully.</p>
                    <p>You can now log in to the Emby server.</p>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="redirectMessage">Redirecting to Emby server in <span id="countdown">3</span> seconds...</span>
                    </div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-success" id="goToEmby">
                        <i class="fas fa-external-link-alt me-2"></i>Go to Emby Now
                    </button>
                    <button type="button" class="btn btn-secondary" id="cancelRedirect">
                        <i class="fas fa-times me-2"></i>Cancel Auto-Redirect
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
