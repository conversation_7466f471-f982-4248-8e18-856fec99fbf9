<?php
/**
 * Quick Debug Script for Emby Registration System
 */

echo "<h1>Debug Information</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// Test 1: Basic PHP Info
echo "<h2>1. PHP Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";

// Test 2: File Existence
echo "<h2>2. File Check</h2>";
$files = ['config.php', 'functions.php', 'register.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✅ $file exists</span><br>";
    } else {
        echo "<span class='error'>❌ $file missing</span><br>";
    }
}

// Test 3: Include Files
echo "<h2>3. Include Test</h2>";
try {
    require_once 'config.php';
    echo "<span class='success'>✅ config.php loaded</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ config.php error: " . $e->getMessage() . "</span><br>";
}

try {
    require_once 'functions.php';
    echo "<span class='success'>✅ functions.php loaded</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ functions.php error: " . $e->getMessage() . "</span><br>";
}

// Test 4: Configuration Values
echo "<h2>4. Configuration</h2>";
if (defined('EMBY_SERVER_URL')) {
    echo "Emby Server URL: " . EMBY_SERVER_URL . "<br>";
} else {
    echo "<span class='error'>❌ EMBY_SERVER_URL not defined</span><br>";
}

if (defined('EMBY_API_KEY')) {
    echo "API Key: " . substr(EMBY_API_KEY, 0, 8) . "... (hidden)<br>";
} else {
    echo "<span class='error'>❌ EMBY_API_KEY not defined</span><br>";
}

// Test 5: cURL Test
echo "<h2>5. cURL Test</h2>";
if (function_exists('curl_init')) {
    echo "<span class='success'>✅ cURL available</span><br>";
    
    // Test basic connection
    if (defined('EMBY_SERVER_URL')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, EMBY_SERVER_URL);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "<span class='error'>❌ Connection error: $error</span><br>";
        } else {
            echo "<span class='info'>HTTP Response Code: $httpCode</span><br>";
        }
    }
} else {
    echo "<span class='error'>❌ cURL not available</span><br>";
}

// Test 6: Session Test
echo "<h2>6. Session Test</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<span class='success'>✅ Session active</span><br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "<span class='error'>❌ Session not active</span><br>";
}

// Test 7: Error Log
echo "<h2>7. Recent Errors</h2>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    echo "Error log location: $errorLog<br>";
    $errors = file_get_contents($errorLog);
    $recentErrors = array_slice(explode("\n", $errors), -10);
    foreach ($recentErrors as $error) {
        if (trim($error)) {
            echo "<small>" . htmlspecialchars($error) . "</small><br>";
        }
    }
} else {
    echo "No error log found or accessible<br>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If all tests pass, try accessing <a href='index.html'>index.html</a></li>";
echo "<li>If there are errors, fix them based on the information above</li>";
echo "<li>Check your web server error logs for more details</li>";
echo "</ul>";
?>
