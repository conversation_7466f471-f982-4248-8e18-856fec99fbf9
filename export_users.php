<?php
/**
 * Export Users Data to CSV
 */

require_once 'config.php';
require_once 'functions.php';

// Simple authentication check
session_start();
if (!isset($_SESSION['admin_authenticated']) || !$_SESSION['admin_authenticated']) {
    http_response_code(403);
    die('Access denied');
}

// Check if database logging is enabled
if (!ENABLE_DATABASE_LOGGING) {
    die('Database logging is not enabled');
}

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
        DB_USERNAME,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Get export type
    $exportType = $_GET['type'] ?? 'users';
    
    if ($exportType === 'users') {
        // Export users
        $stmt = $pdo->query("
            SELECT id, username, email, full_name, emby_user_id, is_active, created_at 
            FROM users 
            ORDER BY created_at DESC
        ");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $filename = 'emby_users_' . date('Y-m-d_H-i-s') . '.csv';
        
    } elseif ($exportType === 'logs') {
        // Export registration logs
        $stmt = $pdo->query("
            SELECT id, username, email, ip_address, success, error_message, created_at 
            FROM registration_log 
            ORDER BY created_at DESC
        ");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $filename = 'emby_registration_logs_' . date('Y-m-d_H-i-s') . '.csv';
        
    } else {
        die('Invalid export type');
    }
    
    // Set headers for CSV download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create file pointer
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    if (!empty($data)) {
        // Add header row
        fputcsv($output, array_keys($data[0]));
        
        // Add data rows
        foreach ($data as $row) {
            // Convert boolean values to readable text
            if (isset($row['is_active'])) {
                $row['is_active'] = $row['is_active'] ? 'Active' : 'Inactive';
            }
            if (isset($row['success'])) {
                $row['success'] = $row['success'] ? 'Success' : 'Failed';
            }
            
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
    
} catch (Exception $e) {
    die('Export error: ' . htmlspecialchars($e->getMessage()));
}
?>
