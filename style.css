/**
 * Emby Server Registration System - Custom Styles
 */

/* Global Styles */
body {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 20px 0;
    color: #e0e6ed;
}

.container {
    max-width: 1200px;
}

/* Card Styles */
.card {
    border: 1px solid #2d3748;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    background: rgba(26, 32, 44, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 1.5rem;
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.card-body {
    padding: 2rem;
    background: rgba(26, 32, 44, 0.98);
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #e2e8f0;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #4a5568;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(45, 55, 72, 0.8);
    color: #e2e8f0;
}

.form-control:focus {
    border-color: #63b3ed;
    box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25);
    transform: translateY(-2px);
    background: rgba(45, 55, 72, 0.95);
}

.form-control::placeholder {
    color: #a0aec0;
}

.form-control.is-valid {
    border-color: #48bb78;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2348bb78' d='m2.3 6.73.94-.94 1.38 1.38 3.02-3.02.94.94L2.3 10.27z'/%3e%3c/svg%3e");
    background-color: rgba(45, 55, 72, 0.9);
}

.form-control.is-invalid {
    border-color: #f56565;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23f56565'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
    background-color: rgba(45, 55, 72, 0.9);
}

.input-group .btn {
    border-radius: 0 10px 10px 0;
    border: 2px solid #4a5568;
    border-left: none;
    background: rgba(74, 85, 104, 0.8);
    color: #e2e8f0;
}

.input-group .form-control {
    border-radius: 10px 0 0 10px;
}

.input-group .form-control:focus + .btn {
    border-color: #63b3ed;
}

/* Button Styles */
.btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
    color: #ffffff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    color: #ffffff;
}

.btn-secondary {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
    border: none;
    color: #ffffff;
}

.btn-outline-secondary {
    border-color: #4a5568;
    color: #a0aec0;
    background: transparent;
}

.btn-outline-secondary:hover {
    background-color: rgba(74, 85, 104, 0.2);
    border-color: #718096;
    color: #e2e8f0;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.alert-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    border: 1px solid #fc8181;
}

.alert-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    border: 1px solid #f6ad55;
}

.alert-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: 1px solid #68d391;
}

.alert-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    border: 1px solid #63b3ed;
}

/* Form Check Styles */
.form-check-input {
    border-radius: 4px;
    border: 2px solid #4a5568;
    width: 1.2em;
    height: 1.2em;
    background-color: rgba(45, 55, 72, 0.8);
}

.form-check-input:checked {
    background-color: #4299e1;
    border-color: #4299e1;
}

.form-check-label {
    margin-left: 0.5rem;
    font-size: 0.95rem;
    color: #e2e8f0;
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: 1px solid #2d3748;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    background: rgba(26, 32, 44, 0.98);
    color: #e2e8f0;
}

.modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: 1px solid #2d3748;
    padding: 1.5rem;
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.modal-body {
    padding: 2rem;
    background: rgba(26, 32, 44, 0.98);
}

.modal-footer {
    border-top: 1px solid #2d3748;
    padding: 1.5rem;
    background: rgba(26, 32, 44, 0.98);
}

/* Badge Styles */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    border: 1px solid transparent;
}

/* Loading Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    body {
        padding: 10px 0;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2d3748;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #63b3ed 0%, #4299e1 100%);
}

/* Focus Styles for Accessibility */
.btn:focus,
.form-control:focus,
.form-check-input:focus {
    outline: 2px solid #63b3ed;
    outline-offset: 2px;
}

/* Text Styles */
.text-muted {
    color: #a0aec0 !important;
}

.form-text {
    font-size: 0.875rem;
    color: #a0aec0;
    margin-top: 0.25rem;
}

/* Link Styles */
a {
    color: #63b3ed;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #90cdf4;
    text-decoration: underline;
}

/* Server Status Indicator */
.badge.bg-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border-color: #68d391;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
    border-color: #fc8181;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%) !important;
    color: #ffffff !important;
    border-color: #f6ad55;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%) !important;
    border-color: #a0aec0;
}

/* Animation for form validation */
.was-validated .form-control:invalid,
.form-control.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success icon animation */
.modal-body .fa-check-circle {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Dark Theme Validation Messages */
.invalid-feedback {
    color: #fc8181;
}

.valid-feedback {
    color: #68d391;
}

/* Dark Theme for Bootstrap Close Button */
.btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Dark Theme for Modal Backdrop */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Additional Dark Theme Enhancements */
.text-danger {
    color: #fc8181 !important;
}

.text-success {
    color: #68d391 !important;
}

.text-warning {
    color: #f6ad55 !important;
}

.text-info {
    color: #63b3ed !important;
}

/* Dark Theme for Terms Modal */
.modal h6 {
    color: #e2e8f0;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.modal p, .modal li {
    color: #cbd5e0;
    line-height: 1.6;
}

.modal ul {
    color: #cbd5e0;
}

/* Enhanced Card Shadow for Dark Theme */
.card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* Dark Theme Input Group Enhancements */
.input-group .btn:hover {
    background: rgba(99, 179, 237, 0.2);
    border-color: #63b3ed;
}

/* Dark Theme Button Enhancements */
.btn-success:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(113, 128, 150, 0.3);
}
