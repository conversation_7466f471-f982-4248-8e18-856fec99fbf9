/**
 * Emby Server Registration System - Custom Styles
 */

/* Global Styles - Emby Theme */
body {
    background: linear-gradient(135deg, #101010 0%, #1a1a1a 50%, #0d1117 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', '<PERSON><PERSON>', 'Helvetica Neue', Arial, sans-serif;
    padding: 20px 0;
    color: #ffffff;
}

.container {
    max-width: 1200px;
}

/* Card Styles - Emby Theme */
.card {
    border: 1px solid #333333;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    background: rgba(24, 24, 24, 0.95);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    border: none;
    padding: 1.5rem;
    background: linear-gradient(135deg, #00a4dc 0%, #0078a8 100%);
    color: #ffffff;
}

.card-body {
    padding: 2rem;
    background: rgba(24, 24, 24, 0.98);
}

/* Form Styles - Emby Theme */
.form-label {
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #444444;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(40, 40, 40, 0.9);
    color: #ffffff;
}

.form-control:focus {
    border-color: #00a4dc;
    box-shadow: 0 0 0 0.2rem rgba(0, 164, 220, 0.25);
    background: rgba(40, 40, 40, 1);
    outline: none;
}

.form-control::placeholder {
    color: #888888;
}

.form-control.is-valid {
    border-color: #52c41a;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2352c41a' d='m2.3 6.73.94-.94 1.38 1.38 3.02-3.02.94.94L2.3 10.27z'/%3e%3c/svg%3e");
    background-color: rgba(40, 40, 40, 0.9);
}

.form-control.is-invalid {
    border-color: #ff4d4f;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ff4d4f'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
    background-color: rgba(40, 40, 40, 0.9);
}

.input-group .btn {
    border-radius: 0 4px 4px 0;
    border: 1px solid #444444;
    border-left: none;
    background: rgba(60, 60, 60, 0.9);
    color: #ffffff;
}

.input-group .form-control {
    border-radius: 4px 0 0 4px;
}

.input-group .form-control:focus + .btn {
    border-color: #00a4dc;
}

/* Button Styles - Emby Theme */
.btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
    text-transform: none;
    letter-spacing: normal;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #00a4dc 0%, #0078a8 100%);
    border: none;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 164, 220, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0078a8 0%, #005a7a 100%);
    box-shadow: 0 4px 12px rgba(0, 164, 220, 0.4);
    transform: translateY(-1px);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 164, 220, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    border: none;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #389e0d 0%, #237804 100%);
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
    transform: translateY(-1px);
}

.btn-secondary {
    background: linear-gradient(135deg, #595959 0%, #434343 100%);
    border: none;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(89, 89, 89, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #434343 0%, #262626 100%);
    box-shadow: 0 4px 12px rgba(89, 89, 89, 0.4);
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border: 1px solid #595959;
    color: #ffffff;
    background: transparent;
}

.btn-outline-secondary:hover {
    background-color: rgba(89, 89, 89, 0.2);
    border-color: #434343;
    color: #ffffff;
}

/* Alert Styles - Emby Theme */
.alert {
    border-radius: 4px;
    border: 1px solid transparent;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.alert-danger {
    background: rgba(255, 77, 79, 0.15);
    color: #ff4d4f;
    border-color: #ff4d4f;
}

.alert-warning {
    background: rgba(250, 173, 20, 0.15);
    color: #faad14;
    border-color: #faad14;
}

.alert-success {
    background: rgba(82, 196, 26, 0.15);
    color: #52c41a;
    border-color: #52c41a;
}

.alert-info {
    background: rgba(0, 164, 220, 0.15);
    color: #00a4dc;
    border-color: #00a4dc;
}

/* Form Check Styles - Emby Theme */
.form-check-input {
    border-radius: 3px;
    border: 1px solid #444444;
    width: 1.2em;
    height: 1.2em;
    background-color: rgba(40, 40, 40, 0.9);
}

.form-check-input:checked {
    background-color: #00a4dc;
    border-color: #00a4dc;
}

.form-check-label {
    margin-left: 0.5rem;
    font-size: 0.95rem;
    color: #ffffff;
}

/* Modal Styles - Emby Theme */
.modal-content {
    border-radius: 8px;
    border: 1px solid #333333;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6);
    background: rgba(24, 24, 24, 0.98);
    color: #ffffff;
}

.modal-header {
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #333333;
    padding: 1.5rem;
    background: linear-gradient(135deg, #00a4dc 0%, #0078a8 100%);
    color: #ffffff;
}

.modal-body {
    padding: 2rem;
    background: rgba(24, 24, 24, 0.98);
}

.modal-footer {
    border-top: 1px solid #333333;
    padding: 1.5rem;
    background: rgba(24, 24, 24, 0.98);
}

/* Badge Styles */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    border: 1px solid transparent;
}

/* Loading Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    body {
        padding: 10px 0;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
}

/* Custom Scrollbar - Emby Theme */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #00a4dc 0%, #0078a8 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0078a8 0%, #005a7a 100%);
}

/* Focus Styles for Accessibility - Emby Theme */
.btn:focus,
.form-control:focus,
.form-check-input:focus {
    outline: 2px solid #00a4dc;
    outline-offset: 2px;
}

/* Text Styles - Emby Theme */
.text-muted {
    color: #888888 !important;
}

.form-text {
    font-size: 0.875rem;
    color: #888888;
    margin-top: 0.25rem;
}

/* Link Styles - Emby Theme */
a {
    color: #00a4dc;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #40c4ff;
    text-decoration: underline;
}

/* Server Status Indicator - Emby Theme */
.badge.bg-success {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%) !important;
    border-color: #52c41a;
    color: #ffffff !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%) !important;
    border-color: #ff4d4f;
    color: #ffffff !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #faad14 0%, #d48806 100%) !important;
    color: #ffffff !important;
    border-color: #faad14;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #595959 0%, #434343 100%) !important;
    border-color: #595959;
    color: #ffffff !important;
}

/* Animation for form validation */
.was-validated .form-control:invalid,
.form-control.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success icon animation */
.modal-body .fa-check-circle {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Emby Theme Validation Messages */
.invalid-feedback {
    color: #ff4d4f;
}

.valid-feedback {
    color: #52c41a;
}

/* Emby Theme for Bootstrap Close Button */
.btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Emby Theme for Modal Backdrop */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.85);
}

/* Additional Emby Theme Enhancements */
.text-danger {
    color: #ff4d4f !important;
}

.text-success {
    color: #52c41a !important;
}

.text-warning {
    color: #faad14 !important;
}

.text-info {
    color: #00a4dc !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Emby Theme for Terms Modal */
.modal h6 {
    color: #ffffff;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.modal p, .modal li {
    color: #cccccc;
    line-height: 1.6;
}

.modal ul {
    color: #cccccc;
}

/* Enhanced Card Shadow for Emby Theme */
.card:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* Emby Theme Input Group Enhancements */
.input-group .btn:hover {
    background: rgba(0, 164, 220, 0.2);
    border-color: #00a4dc;
}

/* Emby Logo Animation */
.card-header svg {
    transition: transform 0.3s ease;
}

.card-header:hover svg {
    transform: scale(1.1) rotate(5deg);
}

/* Emby Brand Colors */
.emby-primary {
    color: #00a4dc !important;
}

.emby-secondary {
    color: #52c41a !important;
}

/* Loading Spinner Emby Style */
.fa-spinner {
    color: #00a4dc;
}

/* Enhanced Form Focus for Emby Theme */
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 164, 220, 0.25), 0 2px 8px rgba(0, 164, 220, 0.15);
}
