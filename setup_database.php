<?php
/**
 * Database Setup for Emby Registration System
 * Run this file once to create the necessary database tables
 */

require_once 'config.php';

if (!ENABLE_DATABASE_LOGGING) {
    die("Database logging is disabled in config.php");
}

try {
    // Connect to MySQL server
    $pdo = new PDO(
        "mysql:host=" . DB_HOST,
        DB_USERNAME,
        DB_PASSWORD,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    // Use the database
    $pdo->exec("USE `" . DB_NAME . "`");
    
    // Create registration_log table
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS `registration_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `email` varchar(255) DEFAULT NULL,
            `ip_address` varchar(45) NOT NULL,
            `success` tinyint(1) NOT NULL DEFAULT 0,
            `error_message` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_username` (`username`),
            KEY `idx_ip_address` (`ip_address`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_success` (`success`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createTableSQL);
    
    // Create rate_limiting table
    $createRateLimitTableSQL = "
        CREATE TABLE IF NOT EXISTS `rate_limiting` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `ip_address` varchar(45) NOT NULL,
            `attempts` int(11) NOT NULL DEFAULT 1,
            `last_attempt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_ip_address` (`ip_address`),
            KEY `idx_last_attempt` (`last_attempt`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createRateLimitTableSQL);
    
    echo "✅ Database setup completed successfully!\n\n";
    echo "Created tables:\n";
    echo "- registration_log: For logging registration attempts\n";
    echo "- rate_limiting: For tracking rate limiting\n\n";
    echo "Database: " . DB_NAME . "\n";
    echo "Host: " . DB_HOST . "\n";
    
} catch (PDOException $e) {
    echo "❌ Database setup failed: " . $e->getMessage() . "\n";
    echo "\nPlease check your database configuration in config.php:\n";
    echo "- DB_HOST: " . DB_HOST . "\n";
    echo "- DB_NAME: " . DB_NAME . "\n";
    echo "- DB_USERNAME: " . DB_USERNAME . "\n";
    echo "- Make sure MySQL server is running\n";
    echo "- Make sure the database user has CREATE privileges\n";
}
?>
