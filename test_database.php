<?php
/**
 * Test Database Connection and Queries
 */

require_once 'config.php';

echo "<h1>Database Connection Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// Check if database logging is enabled
if (!ENABLE_DATABASE_LOGGING) {
    echo "<span class='error'>❌ Database logging is not enabled. Please enable it in config.php</span><br>";
    exit;
}

try {
    // Test basic connection
    echo "<h2>1. Connection Test</h2>";
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
        DB_USERNAME,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "<span class='success'>✅ Database connection successful</span><br>";
    echo "Host: " . DB_HOST . "<br>";
    echo "Database: " . DB_NAME . "<br>";
    echo "Username: " . DB_USERNAME . "<br>";
    
    // Test MySQL version
    echo "<h2>2. MySQL Version</h2>";
    $versionStmt = $pdo->query("SELECT VERSION() as version");
    $version = $versionStmt->fetch(PDO::FETCH_ASSOC);
    echo "MySQL Version: " . $version['version'] . "<br>";
    
    // Test table existence
    echo "<h2>3. Table Structure Test</h2>";
    $tables = ['users', 'registration_log', 'rate_limiting'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<span class='success'>✅ Table '$table': Exists</span><br>";
                
                // Show table structure
                $descStmt = $pdo->query("DESCRIBE $table");
                $columns = $descStmt->fetchAll(PDO::FETCH_ASSOC);
                echo "<small>Columns: ";
                foreach ($columns as $col) {
                    echo $col['Field'] . " (" . $col['Type'] . "), ";
                }
                echo "</small><br>";
                
                // Count records
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $countStmt->fetch(PDO::FETCH_ASSOC);
                echo "<small>Records: " . $count['count'] . "</small><br><br>";
                
            } else {
                echo "<span class='error'>❌ Table '$table': Not found</span><br>";
            }
        } catch (Exception $e) {
            echo "<span class='error'>❌ Table '$table': Error - " . $e->getMessage() . "</span><br>";
        }
    }
    
    // Test LIMIT/OFFSET syntax
    echo "<h2>4. LIMIT/OFFSET Syntax Test</h2>";
    try {
        // Test with direct values
        $stmt1 = $pdo->query("SELECT * FROM users ORDER BY created_at DESC LIMIT 5 OFFSET 0");
        echo "<span class='success'>✅ Direct LIMIT/OFFSET syntax works</span><br>";
        
        // Test with prepared statement (old way)
        try {
            $stmt2 = $pdo->prepare("SELECT * FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?");
            $stmt2->execute([5, 0]);
            echo "<span class='success'>✅ Prepared LIMIT/OFFSET with placeholders works</span><br>";
        } catch (Exception $e) {
            echo "<span class='error'>❌ Prepared LIMIT/OFFSET with placeholders failed: " . $e->getMessage() . "</span><br>";
        }
        
        // Test with variables (new way)
        $limit = 5;
        $offset = 0;
        $stmt3 = $pdo->query("SELECT * FROM users ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        echo "<span class='success'>✅ Direct variable LIMIT/OFFSET syntax works</span><br>";
        
    } catch (Exception $e) {
        echo "<span class='error'>❌ LIMIT/OFFSET test failed: " . $e->getMessage() . "</span><br>";
    }
    
    // Test user queries
    echo "<h2>5. User Query Test</h2>";
    try {
        $userStmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $userStmt->fetch(PDO::FETCH_ASSOC);
        echo "Total users: " . $userCount['count'] . "<br>";
        
        if ($userCount['count'] > 0) {
            $sampleStmt = $pdo->query("SELECT username, email, created_at FROM users ORDER BY created_at DESC LIMIT 3");
            $sampleUsers = $sampleStmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<strong>Sample users:</strong><br>";
            foreach ($sampleUsers as $user) {
                echo "- " . htmlspecialchars($user['username']) . " (" . htmlspecialchars($user['email'] ?: 'no email') . ") - " . $user['created_at'] . "<br>";
            }
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ User query failed: " . $e->getMessage() . "</span><br>";
    }
    
    // Test registration log queries
    echo "<h2>6. Registration Log Test</h2>";
    try {
        $logStmt = $pdo->query("SELECT COUNT(*) as count FROM registration_log");
        $logCount = $logStmt->fetch(PDO::FETCH_ASSOC);
        echo "Total registration attempts: " . $logCount['count'] . "<br>";
        
        if ($logCount['count'] > 0) {
            $recentStmt = $pdo->query("SELECT username, success, created_at FROM registration_log ORDER BY created_at DESC LIMIT 5");
            $recentLogs = $recentStmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<strong>Recent registration attempts:</strong><br>";
            foreach ($recentLogs as $log) {
                $status = $log['success'] ? 'Success' : 'Failed';
                echo "- " . htmlspecialchars($log['username']) . " - $status (" . $log['created_at'] . ")<br>";
            }
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Registration log query failed: " . $e->getMessage() . "</span><br>";
    }
    
    // Test statistics query
    echo "<h2>7. Statistics Query Test</h2>";
    try {
        $statsStmt = $pdo->query("
            SELECT 
                COUNT(*) as total_registrations,
                SUM(success) as successful_registrations,
                COUNT(*) - SUM(success) as failed_registrations
            FROM registration_log
        ");
        $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($stats) {
            echo "Total registrations: " . $stats['total_registrations'] . "<br>";
            echo "Successful: " . ($stats['successful_registrations'] ?: 0) . "<br>";
            echo "Failed: " . ($stats['failed_registrations'] ?: 0) . "<br>";
        } else {
            echo "No statistics data available<br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Statistics query failed: " . $e->getMessage() . "</span><br>";
    }
    
    echo "<h2>Summary</h2>";
    echo "<span class='success'>✅ Database tests completed successfully!</span><br>";
    echo "<p>If you see this message, your database connection and queries are working correctly.</p>";
    
    echo "<h2>Test Links</h2>";
    echo "<ul>";
    echo "<li><a href='admin_console.php'>Admin Console</a></li>";
    echo "<li><a href='view_users.php'>View Users</a></li>";
    echo "<li><a href='test_setup.php'>Setup Test</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</span><br>";
    echo "<br><strong>Common solutions:</strong><br>";
    echo "1. Check if MySQL server is running<br>";
    echo "2. Verify database credentials in config.php<br>";
    echo "3. Make sure the database exists<br>";
    echo "4. Check if the database user has proper permissions<br>";
    echo "5. Run setup_database.php to create tables<br>";
}
?>
