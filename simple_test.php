<?php
/**
 * Simple Registration Test
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Simple Registration Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;}</style>";

try {
    // Test 1: Include files
    echo "<h2>1. Loading Files</h2>";
    require_once 'config.php';
    echo "<span class='success'>✅ config.php loaded</span><br>";
    
    require_once 'functions.php';
    echo "<span class='success'>✅ functions.php loaded</span><br>";
    
    // Test 2: Check configuration
    echo "<h2>2. Configuration Check</h2>";
    echo "Emby Server URL: " . EMBY_SERVER_URL . "<br>";
    echo "API Key: " . substr(EMBY_API_KEY, 0, 8) . "...<br>";
    echo "Registration Enabled: " . (ALLOW_REGISTRATION ? 'Yes' : 'No') . "<br>";
    
    // Test 3: Test Emby connection
    echo "<h2>3. Emby Connection Test</h2>";
    $connectionResult = testEmbyConnection();
    if ($connectionResult) {
        echo "<span class='success'>✅ Emby server connection successful</span><br>";
    } else {
        echo "<span class='error'>❌ Emby server connection failed</span><br>";
        
        // Try to get more details
        $result = makeEmbyApiRequest('System/Info');
        echo "HTTP Code: " . ($result['http_code'] ?? 'unknown') . "<br>";
        echo "Error: " . ($result['error'] ?? 'none') . "<br>";
        echo "Raw Response: " . htmlspecialchars($result['raw_response'] ?? 'empty') . "<br>";
    }
    
    // Test 4: Test user creation (simulation)
    echo "<h2>4. User Creation Test (Simulation)</h2>";
    $testUserData = [
        'username' => 'testuser_' . time(),
        'password' => 'testpass123',
        'email' => '<EMAIL>',
        'fullname' => 'Test User'
    ];
    
    echo "Test data prepared:<br>";
    echo "Username: " . $testUserData['username'] . "<br>";
    echo "Password: [hidden]<br>";
    echo "Email: " . $testUserData['email'] . "<br>";
    echo "Full Name: " . $testUserData['fullname'] . "<br>";
    
    // Check if username exists
    $usernameCheck = checkUsernameExists($testUserData['username']);
    if (isset($usernameCheck['error'])) {
        echo "<span class='error'>❌ Username check failed: " . $usernameCheck['error'] . "</span><br>";
    } else {
        echo "<span class='success'>✅ Username check successful</span><br>";
        echo "Username exists: " . ($usernameCheck['exists'] ? 'Yes' : 'No') . "<br>";
    }
    
    // Test 5: CSRF Token
    echo "<h2>5. CSRF Token Test</h2>";
    $token = generateCSRFToken();
    echo "Generated token: " . htmlspecialchars($token) . "<br>";
    
    $isValid = verifyCSRFToken($token);
    if ($isValid) {
        echo "<span class='success'>✅ CSRF token verification successful</span><br>";
    } else {
        echo "<span class='error'>❌ CSRF token verification failed</span><br>";
    }
    
    // Test 6: Input validation
    echo "<h2>6. Input Validation Test</h2>";
    $testInput = [
        'username' => 'testuser',
        'password' => 'testpass123',
        'confirm_password' => 'testpass123',
        'email' => '<EMAIL>',
        'fullname' => 'Test User',
        'csrf_token' => $token
    ];
    
    $sanitized = sanitizeInput($testInput);
    echo "Sanitized data:<br><pre>" . print_r($sanitized, true) . "</pre>";
    
    $errors = validateInput($sanitized);
    if (empty($errors)) {
        echo "<span class='success'>✅ Input validation passed</span><br>";
    } else {
        echo "<span class='error'>❌ Input validation failed:</span><br>";
        foreach ($errors as $error) {
            echo "- " . htmlspecialchars($error) . "<br>";
        }
    }
    
    echo "<h2>Summary</h2>";
    echo "<p>If all tests above passed, the registration system should work correctly.</p>";
    echo "<p>If any test failed, that's likely the cause of the 'unexpected error'.</p>";
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Fatal Error: " . htmlspecialchars($e->getMessage()) . "</span><br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "Stack trace:<br><pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
