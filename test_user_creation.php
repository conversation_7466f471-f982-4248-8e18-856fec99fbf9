<?php
/**
 * Test User Creation and Password Setting
 */

require_once 'config.php';
require_once 'functions.php';

echo "<h1>Test User Creation and Password Setting</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// Test data
$testUser = [
    'username' => 'testuser_' . time(),
    'password' => 'testpass123',
    'email' => '<EMAIL>',
    'fullname' => 'Test User'
];

echo "<h2>Test Data:</h2>";
echo "Username: " . htmlspecialchars($testUser['username']) . "<br>";
echo "Password: " . htmlspecialchars($testUser['password']) . "<br>";
echo "Email: " . htmlspecialchars($testUser['email']) . "<br>";
echo "Full Name: " . htmlspecialchars($testUser['fullname']) . "<br><br>";

// Test 1: Check Emby connection
echo "<h2>1. Testing Emby Connection</h2>";
if (testEmbyConnection()) {
    echo "<span class='success'>✅ Emby server connection successful</span><br>";
} else {
    echo "<span class='error'>❌ Emby server connection failed</span><br>";
    exit;
}

// Test 2: Create user step by step
echo "<h2>2. Creating User Step by Step</h2>";

// Step 1: Create user without password
echo "<h3>Step 1: Create user without password</h3>";
$userPayload = [
    'Name' => $testUser['username'],
    'Email' => $testUser['email'],
    'FullName' => $testUser['fullname']
];

$result = makeEmbyApiRequest('Users/New', 'POST', $userPayload);

if ($result['success']) {
    echo "<span class='success'>✅ User created successfully</span><br>";
    $userId = $result['data']['Id'];
    echo "User ID: " . htmlspecialchars($userId) . "<br>";
    
    // Step 2: Set password
    echo "<h3>Step 2: Set password</h3>";
    $passwordResult = setEmbyUserPassword($userId, $testUser['password']);
    
    if ($passwordResult['success']) {
        echo "<span class='success'>✅ Password set successfully</span><br>";
        
        // Step 3: Apply policy
        echo "<h3>Step 3: Apply user policy</h3>";
        $policy = DEFAULT_USER_POLICY;
        $policy['IsHidden'] = true;
        
        $policyResult = applyUserPolicy($userId, $policy);
        
        if ($policyResult) {
            echo "<span class='success'>✅ User policy applied successfully</span><br>";
        } else {
            echo "<span class='error'>❌ Failed to apply user policy</span><br>";
        }
        
        // Step 4: Test login
        echo "<h3>Step 4: Test authentication</h3>";
        $authResult = testEmbyAuthentication($testUser['username'], $testUser['password']);
        
        if ($authResult['success']) {
            echo "<span class='success'>✅ Authentication test successful</span><br>";
            echo "Access Token: " . substr($authResult['token'], 0, 20) . "...<br>";
        } else {
            echo "<span class='error'>❌ Authentication test failed: " . htmlspecialchars($authResult['error']) . "</span><br>";
        }
        
        // Step 5: Get user info
        echo "<h3>Step 5: Get user information</h3>";
        $userInfoResult = makeEmbyApiRequest('Users/' . $userId);
        
        if ($userInfoResult['success']) {
            echo "<span class='success'>✅ User information retrieved</span><br>";
            $userInfo = $userInfoResult['data'];
            echo "Name: " . htmlspecialchars($userInfo['Name']) . "<br>";
            echo "Email: " . htmlspecialchars($userInfo['Email'] ?? 'Not set') . "<br>";
            echo "Is Hidden: " . ($userInfo['Policy']['IsHidden'] ? 'Yes' : 'No') . "<br>";
            echo "Is Administrator: " . ($userInfo['Policy']['IsAdministrator'] ? 'Yes' : 'No') . "<br>";
        } else {
            echo "<span class='error'>❌ Failed to get user information</span><br>";
        }
        
        // Cleanup: Delete test user
        echo "<h3>Cleanup: Delete test user</h3>";
        $deleteResult = deleteEmbyUser($userId);
        
        if ($deleteResult) {
            echo "<span class='success'>✅ Test user deleted successfully</span><br>";
        } else {
            echo "<span class='error'>❌ Failed to delete test user</span><br>";
        }
        
    } else {
        echo "<span class='error'>❌ Failed to set password: " . htmlspecialchars($passwordResult['error']) . "</span><br>";
        
        // Cleanup: Delete user if password setting failed
        deleteEmbyUser($userId);
    }
    
} else {
    echo "<span class='error'>❌ Failed to create user: " . htmlspecialchars($result['data']['Message'] ?? 'Unknown error') . "</span><br>";
    echo "HTTP Code: " . $result['http_code'] . "<br>";
}

// Test 3: Test the complete createEmbyUser function
echo "<h2>3. Testing Complete createEmbyUser Function</h2>";

$testUser2 = [
    'username' => 'testuser2_' . time(),
    'password' => 'testpass456',
    'email' => '<EMAIL>',
    'fullname' => 'Test User 2'
];

$completeResult = createEmbyUser($testUser2);

if ($completeResult['success']) {
    echo "<span class='success'>✅ Complete user creation successful</span><br>";
    echo "User ID: " . htmlspecialchars($completeResult['user_id']) . "<br>";
    
    // Test authentication
    $authResult2 = testEmbyAuthentication($testUser2['username'], $testUser2['password']);
    
    if ($authResult2['success']) {
        echo "<span class='success'>✅ Authentication test successful</span><br>";
    } else {
        echo "<span class='error'>❌ Authentication test failed: " . htmlspecialchars($authResult2['error']) . "</span><br>";
    }
    
    // Cleanup
    deleteEmbyUser($completeResult['user_id']);
    echo "<span class='success'>✅ Test user 2 deleted</span><br>";
    
} else {
    echo "<span class='error'>❌ Complete user creation failed: " . htmlspecialchars($completeResult['error']) . "</span><br>";
}

echo "<h2>Test Summary</h2>";
echo "<p>If all tests pass, the user creation and password setting should work correctly.</p>";
echo "<p>If authentication fails, there might be an issue with the password setting process.</p>";

/**
 * Test Emby authentication
 */
function testEmbyAuthentication($username, $password) {
    $endpoint = 'Users/AuthenticateByName';
    
    $data = [
        'Username' => $username,
        'Pw' => $password
    ];
    
    $result = makeEmbyApiRequest($endpoint, 'POST', $data);
    
    if ($result['success']) {
        return [
            'success' => true,
            'token' => $result['data']['AccessToken'] ?? '',
            'user_id' => $result['data']['User']['Id'] ?? ''
        ];
    } else {
        return [
            'success' => false,
            'error' => $result['data']['Message'] ?? 'Authentication failed'
        ];
    }
}
?>
