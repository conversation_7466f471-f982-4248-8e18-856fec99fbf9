<?php
/**
 * Check Username Availability
 */

require_once 'config.php';
require_once 'functions.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['username']) || empty(trim($input['username']))) {
        echo json_encode(['error' => 'Username is required']);
        exit;
    }
    
    $username = trim($input['username']);
    
    // Validate username format
    if (strlen($username) < MIN_USERNAME_LENGTH || strlen($username) > MAX_USERNAME_LENGTH) {
        echo json_encode(['error' => 'Invalid username length']);
        exit;
    }
    
    if (!preg_match('/^[a-zA-Z0-9_-]+$/', $username)) {
        echo json_encode(['error' => 'Invalid username format']);
        exit;
    }
    
    // Check if username exists
    $result = checkUsernameExists($username);
    
    if (isset($result['error'])) {
        echo json_encode(['error' => $result['error']]);
        exit;
    }
    
    echo json_encode([
        'exists' => $result['exists'],
        'username' => $username
    ]);
    
} catch (Exception $e) {
    error_log("Username check error: " . $e->getMessage());
    echo json_encode(['error' => 'Server error']);
}
?>
