<?php
/**
 * Emby Server Registration System - Main Registration Handler
 */

require_once 'config.php';
require_once 'functions.php';

// Set content type
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Check if registration is enabled
if (!ALLOW_REGISTRATION) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => ERROR_MESSAGES['REGISTRATION_DISABLED']]);
    exit;
}

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // Sanitize input
    $data = sanitizeInput($input);
    
    // Verify CSRF token (temporarily disabled for debugging)
    /*
    if (!verifyCSRFToken($data['csrf_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Invalid security token']);
        exit;
    }
    */
    
    // Get client IP
    $clientIP = getClientIP();
    
    // Check rate limiting
    if (!checkRateLimit($clientIP)) {
        http_response_code(429);
        echo json_encode(['success' => false, 'error' => ERROR_MESSAGES['RATE_LIMIT_EXCEEDED']]);
        exit;
    }
    
    // Validate input
    $validationErrors = validateInput($data);
    if (!empty($validationErrors)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Validation failed',
            'errors' => $validationErrors
        ]);
        exit;
    }
    
    // Test Emby server connection
    if (!testEmbyConnection()) {
        http_response_code(503);
        echo json_encode(['success' => false, 'error' => ERROR_MESSAGES['EMBY_CONNECTION_ERROR']]);
        logRegistration($data['username'], $data['email'], $clientIP, false, 'Emby server connection failed');
        exit;
    }
    
    // Check if username already exists
    $usernameCheck = checkUsernameExists($data['username']);
    if (isset($usernameCheck['error'])) {
        http_response_code(503);
        echo json_encode(['success' => false, 'error' => $usernameCheck['error']]);
        logRegistration($data['username'], $data['email'], $clientIP, false, $usernameCheck['error']);
        exit;
    }
    
    if ($usernameCheck['exists']) {
        http_response_code(409);
        echo json_encode(['success' => false, 'error' => ERROR_MESSAGES['USERNAME_EXISTS']]);
        logRegistration($data['username'], $data['email'], $clientIP, false, 'Username already exists');
        exit;
    }
    
    // Create user in Emby
    $userCreationResult = createEmbyUser([
        'username' => $data['username'],
        'password' => $data['password'],
        'email' => $data['email'],
        'fullname' => $data['fullname']
    ]);

    if (!$userCreationResult['success']) {
        $errorMessage = $userCreationResult['error'] ?? ERROR_MESSAGES['EMBY_API_ERROR'];
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $errorMessage]);
        logRegistration($data['username'], $data['email'], $clientIP, false, $errorMessage);
        exit;
    }

    // Save user to database
    $embyUserId = $userCreationResult['user_id'] ?? null;
    $dbSaveResult = saveUserToDatabase([
        'username' => $data['username'],
        'password' => $data['password'],
        'email' => $data['email'],
        'fullname' => $data['fullname']
    ], $embyUserId);

    if (!$dbSaveResult) {
        error_log("Failed to save user to database: " . $data['username']);
        // Continue anyway - Emby user was created successfully
    }

    // Log successful registration
    logRegistration($data['username'], $data['email'], $clientIP, true);
    
    // Send welcome email if enabled
    if (ENABLE_EMAIL_NOTIFICATIONS && !empty($data['email'])) {
        $subject = 'Welcome to Emby Server';
        $message = "
        <html>
        <head>
            <title>Welcome to Emby Server</title>
        </head>
        <body>
            <h2>Welcome to Emby Server!</h2>
            <p>Hello " . ($data['fullname'] ?: $data['username']) . ",</p>
            <p>Your account has been successfully created.</p>
            <p><strong>Username:</strong> {$data['username']}</p>
            <p>You can now log in to the Emby server at: <a href=\"" . EMBY_SERVER_URL . "\">" . EMBY_SERVER_URL . "</a></p>
            <p>Thank you for joining us!</p>
        </body>
        </html>
        ";
        
        sendEmailNotification($data['email'], $subject, $message);
        
        // Notify admin if enabled
        if (!empty(ADMIN_EMAIL)) {
            $adminSubject = 'New Emby User Registration';
            $adminMessage = "
            <html>
            <body>
                <h3>New User Registration</h3>
                <p><strong>Username:</strong> {$data['username']}</p>
                <p><strong>Email:</strong> {$data['email']}</p>
                <p><strong>Full Name:</strong> {$data['fullname']}</p>
                <p><strong>IP Address:</strong> {$clientIP}</p>
                <p><strong>Registration Time:</strong> " . date('Y-m-d H:i:s') . "</p>
            </body>
            </html>
            ";
            
            sendEmailNotification(ADMIN_EMAIL, $adminSubject, $adminMessage);
        }
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => SUCCESS_MESSAGES['REGISTRATION_SUCCESS'],
        'user_id' => $userCreationResult['user_id'] ?? null
    ]);
    
} catch (Exception $e) {
    error_log("Registration error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => ERROR_MESSAGES['GENERAL_ERROR']
    ]);
    
    // Log the error
    if (isset($data['username'])) {
        logRegistration($data['username'] ?? '', $data['email'] ?? '', $clientIP, false, $e->getMessage());
    }
}
?>
