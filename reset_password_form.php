<?php
/**
 * Password Reset Form - Step 2
 */

require_once 'config.php';
require_once 'functions.php';

// Get token from URL
$token = $_GET['token'] ?? '';

if (empty($token)) {
    $error = 'Invalid or missing reset token.';
} else {
    // Verify token
    $resetData = verifyPasswordResetToken($token);
    if (!$resetData) {
        $error = 'Invalid or expired reset token. Please request a new password reset.';
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($error)) {
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verifyCSRFToken($csrfToken)) {
        $error = 'Invalid security token. Please try again.';
    } elseif (empty($newPassword) || strlen($newPassword) < MIN_PASSWORD_LENGTH) {
        $error = 'Password must be at least ' . MIN_PASSWORD_LENGTH . ' characters long.';
    } elseif ($newPassword !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } else {
        // Update password in database and Emby
        $result = updateUserPassword($resetData['user_id'], $newPassword);
        
        if ($result['success']) {
            // Invalidate the reset token
            invalidatePasswordResetToken($token);
            
            // Log successful password reset
            logPasswordResetAttempt($resetData['email'], getClientIP(), true, 'Password updated successfully');
            
            $success = true;
        } else {
            $error = $result['error'] ?? 'Failed to update password. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set New Password - Emby Server</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-5 col-lg-4">
                <div class="card shadow-lg">
                    <div class="card-header text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <svg width="40" height="40" viewBox="0 0 100 100" class="me-3">
                                <circle cx="50" cy="50" r="45" fill="#00a4dc"/>
                                <text x="50" y="60" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">E</text>
                            </svg>
                            <h3 class="mb-0">Set New Password</h3>
                        </div>
                        <p class="mb-0 text-white-50">Enter your new password</p>
                    </div>
                    <div class="card-body">
                        <?php if (isset($success) && $success): ?>
                            <!-- Success Message -->
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle fa-3x mb-3"></i>
                                <h4>Password Updated!</h4>
                                <p class="mb-0">Your password has been successfully updated.</p>
                                <p>You can now log in to Emby with your new password.</p>
                            </div>
                            <div class="d-grid">
                                <a href="http://embyjames.ddns.net:8096/" class="btn btn-success btn-lg">
                                    <i class="fas fa-external-link-alt me-2"></i>Go to Emby Login
                                </a>
                            </div>
                            
                        <?php elseif (isset($error)): ?>
                            <!-- Error Message -->
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                            </div>
                            <div class="text-center">
                                <a href="forgot_password.html" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>Request New Reset Link
                                </a>
                            </div>
                            
                        <?php else: ?>
                            <!-- Password Reset Form -->
                            <form method="POST" novalidate>
                                <!-- CSRF Token -->
                                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars(generateCSRFToken()) ?>">
                                
                                <!-- New Password -->
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>New Password <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="new_password" name="new_password" required
                                               minlength="<?= MIN_PASSWORD_LENGTH ?>" placeholder="Enter your new password">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        Password must be at least <?= MIN_PASSWORD_LENGTH ?> characters long.
                                    </div>
                                </div>
                                
                                <!-- Confirm Password -->
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Confirm New Password <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required
                                               minlength="<?= MIN_PASSWORD_LENGTH ?>" placeholder="Confirm your new password">
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>Update Password
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <!-- Back to Login -->
                        <div class="text-center mt-3">
                            <a href="http://embyjames.ddns.net:8096/" class="btn btn-link">
                                <i class="fas fa-arrow-left me-1"></i>Back to Emby Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password toggle functionality
        document.getElementById('togglePassword')?.addEventListener('click', function() {
            togglePasswordVisibility('new_password', this);
        });
        
        document.getElementById('toggleConfirmPassword')?.addEventListener('click', function() {
            togglePasswordVisibility('confirm_password', this);
        });
        
        function togglePasswordVisibility(fieldId, button) {
            const field = document.getElementById(fieldId);
            const icon = button.querySelector('i');
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        // Real-time password confirmation validation
        document.getElementById('confirm_password')?.addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
                if (confirmPassword) {
                    this.classList.add('is-valid');
                }
            }
        });
    </script>
</body>
</html>
