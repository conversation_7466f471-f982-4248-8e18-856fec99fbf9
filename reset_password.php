<?php
/**
 * Password Reset Request Handler
 */

require_once 'config.php';
require_once 'functions.php';

// Set content type
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // Sanitize input
    $email = trim(strip_tags($input['email'] ?? ''));
    $csrfToken = $input['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verifyCSRFToken($csrfToken)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Invalid security token']);
        exit;
    }
    
    // Validate email
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Please enter a valid email address']);
        exit;
    }
    
    // Get client IP
    $clientIP = getClientIP();
    
    // Check rate limiting for password reset
    if (!checkPasswordResetRateLimit($clientIP)) {
        http_response_code(429);
        echo json_encode(['success' => false, 'error' => 'Too many password reset attempts. Please try again later.']);
        exit;
    }
    
    // Check if user exists in database
    $user = getUserByEmail($email);
    
    if (!$user) {
        // Don't reveal if email exists or not for security
        echo json_encode([
            'success' => true,
            'message' => 'If an account with this email exists, you will receive a password reset link.'
        ]);
        
        // Log the attempt
        logPasswordResetAttempt($email, $clientIP, false, 'Email not found');
        exit;
    }
    
    // Generate reset token
    $resetToken = generatePasswordResetToken();
    $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1 hour expiry
    
    // Save reset token to database
    if (!savePasswordResetToken($user['id'], $resetToken, $expiresAt)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to generate reset token. Please try again.']);
        exit;
    }
    
    // Send reset email
    $resetLink = getBaseUrl() . '/reset_password_form.php?token=' . $resetToken;
    $emailSent = sendPasswordResetEmail($email, $user['username'], $resetLink);
    
    if (!$emailSent) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to send reset email. Please contact administrator.']);
        logPasswordResetAttempt($email, $clientIP, false, 'Email send failed');
        exit;
    }
    
    // Log successful attempt
    logPasswordResetAttempt($email, $clientIP, true);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Password reset link has been sent to your email address.'
    ]);
    
} catch (Exception $e) {
    error_log("Password reset error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'An unexpected error occurred. Please try again.'
    ]);
}
?>
