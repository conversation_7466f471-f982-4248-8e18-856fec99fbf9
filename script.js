/**
 * Emby Server Registration System - Client-side JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    // Get CSRF token
    fetchCSRFToken();
    
    // Check server status
    checkServerStatus();
    
    // Set up event listeners
    setupEventListeners();
    
    // Set up form validation
    setupFormValidation();
}

function fetchCSRFToken() {
    fetch('get_csrf_token.php')
        .then(response => response.json())
        .then(data => {
            if (data.token) {
                document.getElementById('csrfToken').value = data.token;
            }
        })
        .catch(error => {
            console.error('Error fetching CSRF token:', error);
        });
}

function checkServerStatus() {
    const statusElement = document.getElementById('serverStatus');
    
    fetch('check_server.php')
        .then(response => response.json())
        .then(data => {
            if (data.online) {
                statusElement.textContent = 'Online';
                statusElement.className = 'badge bg-success';
            } else {
                statusElement.textContent = 'Offline';
                statusElement.className = 'badge bg-danger';
                showAlert('warning', 'Server is currently offline. Registration may not work properly.');
            }
        })
        .catch(error => {
            statusElement.textContent = 'Unknown';
            statusElement.className = 'badge bg-warning';
            console.error('Error checking server status:', error);
        });
}

function setupEventListeners() {
    // Form submission
    document.getElementById('registrationForm').addEventListener('submit', handleFormSubmit);
    
    // Password toggle buttons
    document.getElementById('togglePassword').addEventListener('click', function() {
        togglePasswordVisibility('password', this);
    });
    
    document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        togglePasswordVisibility('confirmPassword', this);
    });
    
    // Real-time password confirmation validation
    document.getElementById('confirmPassword').addEventListener('input', validatePasswordMatch);
    
    // Username availability check
    document.getElementById('username').addEventListener('blur', checkUsernameAvailability);
    
    // Login link
    document.getElementById('loginLink').addEventListener('click', function(e) {
        e.preventDefault();
        // You can customize this to redirect to your Emby server login
        window.open('/emby', '_blank');
    });
    
    // Go to Emby button in success modal
    document.getElementById('goToEmby').addEventListener('click', function() {
        window.open('/emby', '_blank');
    });
}

function setupFormValidation() {
    const form = document.getElementById('registrationForm');
    const inputs = form.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });
}

function validateField(field) {
    const isValid = field.checkValidity();
    
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
    }
    
    return isValid;
}

function validatePasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmField = document.getElementById('confirmPassword');
    
    if (confirmPassword && password !== confirmPassword) {
        confirmField.setCustomValidity('Passwords do not match');
        confirmField.classList.add('is-invalid');
        confirmField.classList.remove('is-valid');
    } else {
        confirmField.setCustomValidity('');
        if (confirmPassword) {
            confirmField.classList.remove('is-invalid');
            confirmField.classList.add('is-valid');
        }
    }
}

function checkUsernameAvailability() {
    const username = document.getElementById('username').value.trim();
    const usernameField = document.getElementById('username');
    
    if (username.length < 3) return;
    
    // Show loading state
    usernameField.classList.add('is-loading');
    
    fetch('check_username.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: username })
    })
    .then(response => response.json())
    .then(data => {
        usernameField.classList.remove('is-loading');
        
        if (data.exists) {
            usernameField.setCustomValidity('Username already exists');
            usernameField.classList.add('is-invalid');
            usernameField.classList.remove('is-valid');
        } else {
            usernameField.setCustomValidity('');
            usernameField.classList.remove('is-invalid');
            usernameField.classList.add('is-valid');
        }
    })
    .catch(error => {
        usernameField.classList.remove('is-loading');
        console.error('Error checking username:', error);
    });
}

function togglePasswordVisibility(fieldId, button) {
    const field = document.getElementById(fieldId);
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function handleFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = document.getElementById('submitBtn');
    
    // Validate form
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // Show loading state
    setLoadingState(submitBtn, true);
    
    // Collect form data
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Submit registration
    fetch('register.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        setLoadingState(submitBtn, false);
        
        if (data.success) {
            showSuccessModal();
            form.reset();
            form.classList.remove('was-validated');
        } else {
            if (data.errors && Array.isArray(data.errors)) {
                showAlert('danger', data.errors.join('<br>'));
            } else {
                showAlert('danger', data.error || 'Registration failed. Please try again.');
            }
        }
    })
    .catch(error => {
        setLoadingState(submitBtn, false);
        console.error('Registration error:', error);
        showAlert('danger', 'An unexpected error occurred. Please try again.');
    });
}

function setLoadingState(button, loading) {
    if (loading) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
    } else {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-user-plus me-2"></i>Create Account';
    }
}

function showAlert(type, message) {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    alertContainer.innerHTML = alertHTML;
    
    // Auto-dismiss after 10 seconds
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 10000);
    
    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function showSuccessModal() {
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
    successModal.show();
}

// Utility function to debounce input events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add loading CSS for username field
const style = document.createElement('style');
style.textContent = `
    .form-control.is-loading {
        background-image: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23007bff' fill-opacity='0.5'%3e%3cpath d='M10 3v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6z'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px 16px;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
